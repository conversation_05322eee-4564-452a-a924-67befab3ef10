import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { validateAuth } from './lib/middleware/auth';

// Paths that don't require authentication
const publicPaths = [
  '/login',
  '/signup',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/google',
  '/api/auth/google/callback',
  '/api/auth/twitter',
  '/api/auth/twitter/callback',
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public paths
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }

  // API routes starting with /api that aren't in publicPaths require auth
  if (pathname.startsWith('/api')) {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    return NextResponse.next();
  }

  // Check authentication for all other routes
  const user = await validateAuth(request);
  if (!user) {
    // Redirect to login page with the original URL as redirect parameter
    const redirectUrl = new URL('/login', request.url);
    redirectUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  return NextResponse.next();
}

// Configure paths that should be matched by middleware
export const config = {
  matcher: [
    /*
     * Match all paths except:
     * 1. /api/auth/* (authentication endpoints)
     * 2. Static files
     * 3. Favicon
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};