# Tweetly-NG: Unified AI-Powered Twitter/X Management Platform - Comprehensive Product Requirements Document

## Project Title
**Tweetly-NG: Unified AI-Powered Twitter/X Management Platform (Next.js Full-Stack)**

## Project Overview
Build "xtasker," a comprehensive, developer-focused web application for AI-assisted Twitter/X management. This platform will enable users to compose, schedule, and publish tweets, manage AI personalities ("bots"), and organize a knowledge base ("Tweet Brain"). The application MUST be a **unified full-stack Next.js application** (using the App Router), where the frontend dashboard and backend API logic reside within a single Next.js project. This approach aims to simplify development, deployment, and CORS management. The platform will integrate with OpenAI and Google Generative AI (using the official `@google/generative-ai` SDK), offering advanced customization for models and embeddings. Data persistence will be handled by a PostgreSQL database, specifically utilizing the `pgvector` extension for semantic search capabilities.

## Core Objective
Develop a robust, user-friendly, and performant platform for AI-assisted tweet composition, scheduling, publishing, and knowledge management, all within a single, cohesive Next.js application. The UI/UX should draw heavily from modern design patterns with shadcn/ui components, adapted for the Next.js architecture.

## Current Implementation Status
- ✅ Basic Next.js app structure with TypeScript and App Router
- ✅ PostgreSQL database with Prisma ORM and pgvector extension enabled
- ✅ Authentication system with Passport.js (Google/Twitter OAuth + local auth)
- ✅ AI provider integrations (Google GenAI, OpenAI) with enhanced error handling
- ✅ Twitter API v2 integration with improved authentication and error handling
- ✅ Basic UI components and layouts with theme switching
- ✅ Database schema with User, TwitterAccount, BotPersona, ScheduledTweet, BrainEntry models
- ✅ Enhanced Google GenAI integration with latest models (gemini-2.0-flash-exp)
- ✅ Improved Twitter API error handling with specific error types
- ⚠️ Partial implementation of dashboard pages and components
- ❌ Missing: Complete UI implementation, API routes, business logic, semantic search

## I. Key Features (Detailed Requirements)

### 1. User Authentication & Authorization (Next.js API Routes & Frontend)
**Providers:**
- Email/Password (secure password hashing with `bcryptjs`)
- Google OAuth 2.0 (using `passport` and `passport-google-oauth20`)
- Twitter/X OAuth 1.0a or 2.0 (using `passport` and `passport-twitter`, ensuring it supports posting on behalf of the user)

**Session Management:** Secure session handling (e.g., JWTs stored in httpOnly cookies, managed via Next.js middleware or API route helpers)

**Callback Handling:** OAuth callbacks must be correctly handled within Next.js API routes

**User Profile:** Basic profile management (display name, email, connected accounts, profile picture)

**Auth UI:** Implement login, signup, password reset, and OTP verification pages with modern design

### 2. AI Provider Integration & Configuration (Next.js API Routes & Frontend Settings)
**Multi-Provider Support:**

**OpenAI:**
- Frontend UI (in `/dashboard/integrations`) for users to input their OpenAI API Key
- Backend API to validate and store this key securely
- Support for custom `baseURL` (for proxies or self-hosted compatible models)
- Support for specifying custom model names for chat/completions (e.g., "gpt-4-turbo-preview", "mistralai/mixtral-8x7b-instruct")
- Support for specifying custom embedding model names (e.g., "text-embedding-3-small", "hkunlp/instructor-xl")

**Google Generative AI:**
- Integrate using the official **`@google/generative-ai` JavaScript SDK**
- Frontend UI for users to input their Google AI Studio API Key
- Backend API to validate and store this key
- Support for specifying models (e.g., "gemini-2.0-flash-exp" for text, "text-embedding-004" for embeddings)

**API Key Management:** User-provided API keys (OpenAI, Google GenAI) must be stored securely in the PostgreSQL database, encrypted at rest, and associated with the user account

**Provider Selection:** Allow users to select their preferred AI provider and specific models for different tasks (e.g., bot generation, Tweet Brain embeddings) via the dashboard settings

### 3. AI Bot System (Next.js API Routes & Frontend Dashboard)
**Bot Persona Definition:**

**Primary Method: Personality File Upload (CRITICAL):**
- Allow users to upload a personality definition file (e.g., `.txt`, `.md`, or `.json`) via the UI
- **File Content Structure (JSON preferred):**
```json
{
  "name": "Bot Name",
  "description": "Short bio for the bot.",
  "style_guide": {
    "tone": "witty and informal",
    "voice": "first-person",
    "language_complexity": "intermediate",
    "emoji_usage": "moderate",
    "hashtag_usage": "relevant_only",
    "common_phrases": ["Hey devs!", "Quick tip:"],
    "length_preference": "short_and_punchy"
  },
  "example_outputs": [
    "Example tweet 1 showcasing the style.",
    "Example tweet 2 demonstrating tone.",
    "Example tweet 3 with typical hashtag usage."
  ],
  "core_topics": ["web development", "AI in coding", "Next.js"],
  "keywords_to_avoid": ["outdated tech", "negative sentiment words"],
  "target_audience": "Developers, tech enthusiasts, and students."
}
```

**Bot Management:** CRUD operations for user-created/configured bots with modern UI components

**Bot Interaction:** A chat-like interface where users can interact with their configured bots. The selected AI provider/model (OpenAI or Google GenAI) will power the responses

**Contextual Generation:** For bot responses, fetch relevant context to guide the AI's tone and style

### 4. Tweet Composer & Publishing (Next.js API Routes & Frontend Dashboard)
- Rich text editor for composing tweets with modern UI
- Character count, emoji picker
- Media Upload: Support for uploading images (and potentially GIFs/videos) using Next.js API routes for handling uploads
- Direct Publishing: Post tweets immediately to connected Twitter/X accounts via Next.js API routes
- Error handling for API failures from Twitter/X with specific error types
- Modern UI for publishing with shadcn/ui components

### 5. Tweet Scheduling (Next.js API Routes & Frontend Dashboard)
- Allow users to schedule tweets for a future date and time with date/time picker components
- A robust backend cron job or task scheduler (using `node-cron` within a Next.js API route) to handle publishing scheduled tweets
- View/edit/delete scheduled tweets with modern table components
- Bulk scheduling capabilities
- Retry logic for failed publications with exponential backoff

### 6. Tweet Brain (Knowledge Base - Next.js API Routes & Frontend Dashboard)
**Storage:** Allow users to save their own tweets, interesting tweets from others (e.g., by URL), or text snippets into their "Brain"

**Categorization/Tagging:** Users can create categories and/or tags to organize Brain entries

**Vector Embeddings & Semantic Search (CRITICAL with PostgreSQL + `pgvector`):**
- When an entry is added to the Brain, generate a vector embedding for its content using the user's selected AI provider/embedding model (OpenAI or Google GenAI)
- Store this embedding in the PostgreSQL database using the `pgvector` extension
- Implement a semantic search feature allowing users to search their Brain using natural language queries
- The backend should convert the search query to an embedding and perform a similarity search against the stored embeddings in PostgreSQL

**Display:** Masonry layout or list view for Brain entries with modern UI components

### 7. Dashboard UI (Next.js App Router)
- Structure the dashboard with modern layout components
- Implement main sections: Home (`/home`), Brain (`/brain`), Publish (`/publish`), Schedule (`/schedule`), Bots (`/bots`), Integrations (`/integrations`), Help (`/help`)
- Utilize shadcn/ui components for consistent design
- Implement theme switching (light/dark) with `next-themes`
- Notifications for actions with toast components

## II. Technology Stack & Architecture (Unified Next.js)

### 1. Framework
**Next.js (latest stable version, App Router)** with TypeScript
- Backend API logic will be implemented as Next.js API Routes
- Frontend will be React components within the Next.js `app` directory

### 2. Database
**PostgreSQL (version 14+)** - Consider NeonJS or similar serverless PostgreSQL for ease of deployment
**`pgvector` extension:** Must be enabled and used for vector fields

### 3. ORM
**Prisma:** For type-safe database access, schema management, and migrations. The schema needs to be adapted for PostgreSQL and `pgvector`

### 4. AI SDKs
- OpenAI: `openai` npm package
- Google Generative AI: **`@google/generative-ai`** npm package (updated to latest version)

### 5. Frontend UI
- React, TypeScript
- Tailwind CSS with shadcn/ui components for modern design
- State Management: Context API or Zustand for complex state
- Form Handling: React Hook Form
- API Communication: `axios` or native `fetch` within client components or Server Actions

### 6. Backend Libraries (for Next.js API Routes)
- `passport` (and relevant strategies: `passport-google-oauth20`, `passport-twitter`)
- `jsonwebtoken` for JWTs
- `bcryptjs` for password hashing
- `node-cron` (or serverless-friendly alternative like Vercel Cron Jobs) for scheduling
- File upload handling with Next.js API route's built-in body parsing

### 7. Development & Build
- `pnpm` or `yarn` for package management
- ESLint and Prettier for code quality
- TypeScript for type safety

## III. Data Models (Prisma Schema for PostgreSQL with `pgvector`)

### Core Models
**User:** (id, email, hashedPassword, name, googleId, twitterId, profilePicture, createdAt, updatedAt, openAiApiKey (encrypted), googleApiKey (encrypted), defaultAiProvider, defaultChatModel, defaultEmbeddingModel)

**TwitterAccount:** (id, userId (FK to User), twitterUserId_str, screenName, name, profilePicture, accessToken, refreshToken, createdAt, updatedAt)

**BotPersona:** (id, userId (FK to User), name, description, **personaContent (JSONB for storing parsed personality file)**, personaType: 'FILE' | 'SCRAPE', aiProvider: 'OPENAI' | 'GOOGLE', chatModel, embeddingModel, createdAt, updatedAt)

**ScheduledTweet:** (id, userId (FK to User), content (TEXT), mediaUrlsJson (JSONB), scheduledAt (TIMESTAMPTZ), status: 'PENDING' | 'PUBLISHED' | 'FAILED', twitterAccountId (FK to TwitterAccount), botPersonaId (FK to BotPersona, optional), createdAt, updatedAt, publishedTweetId (TEXT, optional))

**BrainEntry:** (id, userId (FK to User), title (TEXT, optional), content (TEXT), sourceUrl (TEXT, optional), **embedding Vector(1536)** (or appropriate dimension for chosen model), categoryId (FK to BrainCategory, optional), createdAt, updatedAt)

**BrainCategory:** (id, userId (FK to User), name (TEXT), createdAt, updatedAt)

**UploadedFile:** (id, userId (FK to User), fileName, fileType, fileSize, fileURL, createdAt) - For tweet media

**Token:** (id, userId (FK to User), token (TEXT, unique), type: 'SESSION' | 'PASSWORD_RESET', expiresAt (TIMESTAMPTZ))

## IV. API Design (Next.js API Routes)

### Authentication Routes
- `/api/auth/google` - Google OAuth initiation and callback
- `/api/auth/twitter` - Twitter OAuth initiation and callback
- `/api/auth/login` - Email/password login
- `/api/auth/register` - User registration
- `/api/auth/logout` - User logout
- `/api/auth/me` - Get current user
- `/api/auth/otp-verify` - OTP verification
- `/api/auth/password-reset` - Password reset functionality

### AI Configuration Routes
- `/api/ai-config/keys` - PUT for setting API keys
- `/api/ai-config/models` - GET available models
- `/api/ai-config/validate` - Validate API keys

### Bot Management Routes
- `/api/bots/*` - CRUD for `BotPersona`
- `/api/bots/{id}/upload-persona` - POST personality file upload
- `/api/bots/{botId}/generate` - POST with context/prompt for AI generation
- `/api/bots/{botId}/chat` - POST for chat interactions

### Tweet Management Routes
- `/api/tweets/publish` - POST direct tweet publishing
- `/api/tweets/schedule` - POST, GET, PUT `/{id}`, DELETE `/{id}` for scheduled tweets
- `/api/tweets/drafts` - CRUD for draft tweets

### Brain/Knowledge Base Routes
- `/api/brain/entries` - POST, GET, PUT `/{id}`, DELETE `/{id}` for brain entries
- `/api/brain/search` - GET with `query` parameter for semantic search
- `/api/brain/categories` - CRUD for categories
- `/api/brain/embeddings` - POST to generate embeddings

### Media Routes
- `/api/media/upload` - POST for tweet media uploads

## V. User Experience (UX) & UI Structure

### Global UI Elements & Layouts

**Root Layout (`app/layout.tsx`):**
- Wraps the entire application
- Includes global context providers (Theme, Auth, Notification, AI, X, Brain, Media)
- Sets up global CSS and fonts

**Authentication Layout (`app/(auth)/layout.tsx`):**
- Used for Login, Signup, Password Reset, OTP Verification pages
- Centered content area with modern design
- Displays application logo and branding

**Main Dashboard Layout (`app/(main)/dashboard/layout.tsx`):**
- Used for all authenticated dashboard sections
- **Components:**
  - **Sidebar:** Collapsible navigation sidebar with shadcn/ui components
  - **Navbar:** Top navigation bar with user profile, theme switcher, connection status
- Content area for specific dashboard pages

### Key Pages & UI Flows

**Authentication Pages (`app/(auth)/`):**
- Login with email/password and OAuth options
- Signup with validation and OTP verification
- Password reset with email verification
- Modern form design with shadcn/ui components

**Dashboard Home (`app/(main)/dashboard/home/<USER>
- Welcome message and onboarding checklist
- Summary cards for Tweet Brain stats, active bots, scheduled posts
- Quick actions and navigation to key features

**Integrations (`app/(main)/dashboard/integrations/page.tsx`):**
- Grid layout of integration cards
- Twitter account connection/disconnection
- OpenAI and Google AI API key configuration
- Status indicators and validation feedback

**Bots Section (`app/(main)/dashboard/bots/`):**
- Sub-navigation for "Your Bots", "Chat", "Training"
- Grid display of bot cards for management
- Chat interface with bot selection and conversation history
- Training interface for personality file upload

**Tweet Brain Section (`app/(main)/dashboard/brain/`):**
- Sub-navigation for "View", "Categories", "AI Search"
- Masonry layout for content display
- Category management with modern UI
- AI-powered semantic search interface

**Publish Section (`app/(main)/dashboard/publish/`):**
- Rich text editor with character count and emoji picker
- Media upload and preview
- Scheduling options with date/time picker
- Draft management

**Schedule Section (`app/(main)/dashboard/schedule/`):**
- Table view of scheduled and posted tweets
- Bulk operations and management actions
- Status indicators and retry options

## VI. Specific Instructions & Constraints

1. **Unified Next.js Architecture:** Strict requirement - no separate frontend/backend projects
2. **PostgreSQL & `pgvector`:** Database choice is fixed - ensure Prisma schema and queries are compatible
3. **Google GenAI SDK:** Use `@google/generative-ai` with latest models (gemini-2.0-flash-exp)
4. **Personality File Focus:** Prioritize personality file upload method for bot creation
5. **Security:** Encrypt API keys in database, use httpOnly cookies for session tokens
6. **Modularity:** Organize code logically within Next.js project structure
7. **Modern UI:** Use shadcn/ui components for consistent, modern design
8. **Error Handling:** Implement comprehensive error handling with specific error types
9. **Performance:** Optimize for fast loading and responsive interactions

## VII. Error Handling & Fallback Strategies

### System-Wide Error Handling Architecture
- Client-side error boundaries with fallback UI
- API request error handling with retry logic
- Global error middleware for server-side errors
- Service layer error handling with fallback providers
- Database retry logic for connection issues
- Scheduled tweet recovery for failed publications
- Brain entry vector regeneration for failed embeddings
- Monitoring and alerting for system health

## VIII. Success Criteria
- Complete, functional web application with all core features implemented
- Responsive design working seamlessly on desktop and tablet devices
- Secure authentication and data handling with encrypted API keys
- Reliable tweet scheduling and publishing with retry mechanisms
- Fast semantic search with accurate results using pgvector
- Intuitive user interface with excellent UX using shadcn/ui components
- Comprehensive error handling and recovery mechanisms
- Production-ready deployment configuration
- High performance with optimized loading times
- Scalable architecture supporting multiple users

## IX. Priority Implementation Order
1. **Phase 1 (Critical):** Complete authentication system and secure session management
2. **Phase 2 (Core):** Functional tweet composer, publishing, and basic scheduling
3. **Phase 3 (AI):** AI bot integration with persona management and chat interface
4. **Phase 4 (Knowledge):** Tweet Brain with semantic search and vector embeddings
5. **Phase 5 (UI/UX):** Complete dashboard UI with all sections and modern design
6. **Phase 6 (Integration):** Integrations management and API key configuration
7. **Phase 7 (Advanced):** Advanced features, analytics, and optimizations
8. **Phase 8 (Polish):** Error handling refinement, performance optimization, and deployment
