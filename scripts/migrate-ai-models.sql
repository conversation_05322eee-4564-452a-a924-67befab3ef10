-- Migration script to update AI model configurations
-- Run this after updating the codebase to migrate existing users

-- Update users with old Google models to new ones
UPDATE "User" 
SET 
  "defaultChatModel" = 'gemini-2.0-flash-exp',
  "defaultEmbeddingModel" = 'text-embedding-004'
WHERE 
  "defaultAiProvider" = 'GOOGLE' 
  AND "defaultChatModel" = 'gemini-pro';

-- Update users with old OpenAI models to latest versions
UPDATE "User" 
SET 
  "defaultChatModel" = 'gpt-4-turbo-preview',
  "defaultEmbeddingModel" = 'text-embedding-3-small'
WHERE 
  "defaultAiProvider" = 'OPENAI' 
  AND "defaultChatModel" IN ('gpt-4', 'gpt-3.5-turbo-16k');

-- Update bot personas to use new Google models
UPDATE "BotPersona" 
SET 
  "chatModel" = 'gemini-2.0-flash-exp',
  "embeddingModel" = 'text-embedding-004'
WHERE 
  "aiProvider" = 'GOOGLE' 
  AND ("chatModel" = 'gemini-pro' OR "chatModel" IS NULL);

-- Set default AI provider to Google for new users (already done in schema)
-- This is just for documentation purposes

-- Optional: Migrate users from OpenAI to Google as default
-- Uncomment the following lines if you want to migrate all users to Google AI
/*
UPDATE "User" 
SET 
  "defaultAiProvider" = 'GOOGLE',
  "defaultChatModel" = 'gemini-2.0-flash-exp',
  "defaultEmbeddingModel" = 'text-embedding-004'
WHERE 
  "defaultAiProvider" = 'OPENAI'
  AND "googleApiKey" IS NOT NULL;
*/

-- Verify the migration
SELECT 
  "defaultAiProvider",
  "defaultChatModel",
  "defaultEmbeddingModel",
  COUNT(*) as user_count
FROM "User" 
GROUP BY "defaultAiProvider", "defaultChatModel", "defaultEmbeddingModel"
ORDER BY "defaultAiProvider", user_count DESC;
