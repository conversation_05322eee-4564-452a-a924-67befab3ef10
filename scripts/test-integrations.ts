/**
 * Integration Test Script
 * Run this to verify Google GenAI and Twitter API integrations
 * 
 * Usage: npx tsx scripts/test-integrations.ts
 */

import { GoogleAIProvider } from '../lib/ai/providers/google-genai';
import { TwitterApi } from 'twitter-api-v2';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testGoogleAI() {
  console.log('🧪 Testing Google AI Integration...');
  
  const apiKey = process.env.GEMINI_API_KEY;
  if (!apiKey) {
    console.error('❌ GEMINI_API_KEY not found in environment');
    return false;
  }

  try {
    const provider = new GoogleAIProvider({
      apiKey,
      defaultChatModel: 'gemini-2.0-flash-exp',
      defaultEmbeddingModel: 'text-embedding-004'
    });

    // Test configuration validation
    console.log('  ⏳ Validating configuration...');
    await provider.validateConfig();
    console.log('  ✅ Configuration valid');

    // Test chat completion
    console.log('  ⏳ Testing chat completion...');
    const chatResponse = await provider.generateChatCompletion([
      { role: 'user', content: 'Say "Hello from Google AI!" in exactly those words.' }
    ]);
    console.log('  ✅ Chat completion:', chatResponse);

    // Test embedding generation
    console.log('  ⏳ Testing embedding generation...');
    const embedding = await provider.generateEmbedding('Test embedding text');
    console.log('  ✅ Embedding generated, length:', embedding.length);

    // Test streaming (if available)
    try {
      console.log('  ⏳ Testing streaming...');
      const streamResult = await provider.generateChatCompletionStream([
        { role: 'user', content: 'Count from 1 to 3' }
      ]);
      console.log('  ✅ Streaming supported');
    } catch (error) {
      console.log('  ⚠️ Streaming not available:', error instanceof Error ? error.message : 'Unknown error');
    }

    return true;
  } catch (error) {
    console.error('  ❌ Google AI test failed:', error instanceof Error ? error.message : error);
    return false;
  }
}

async function testTwitterAPI() {
  console.log('🐦 Testing Twitter API Integration...');
  
  const clientId = process.env.x_CLIENT_ID;
  const clientSecret = process.env.x_CLIENT_SECRET;
  
  if (!clientId || !clientSecret) {
    console.error('❌ Twitter credentials not found in environment');
    return false;
  }

  try {
    // Test with app-only authentication (Bearer token)
    const client = new TwitterApi({
      appKey: clientId,
      appSecret: clientSecret,
    });

    // Get app-only client
    const appOnlyClient = await client.appLogin();
    
    console.log('  ⏳ Testing API connection...');
    
    // Test basic API call
    const user = await appOnlyClient.v2.userByUsername('twitter');
    console.log('  ✅ API connection successful, found user:', user.data?.name);

    // Test rate limit info
    const rateLimits = await appOnlyClient.v1.rateLimitStatuses();
    console.log('  ✅ Rate limits accessible');

    return true;
  } catch (error) {
    console.error('  ❌ Twitter API test failed:', error instanceof Error ? error.message : error);
    return false;
  }
}

async function testPassportAuth() {
  console.log('🔐 Testing Passport.js Configuration...');
  
  const googleClientId = process.env.GOOGLE_CLIENT_ID;
  const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
  const twitterClientId = process.env.x_CLIENT_ID;
  const twitterClientSecret = process.env.x_CLIENT_SECRET;
  
  let allConfigured = true;
  
  if (!googleClientId || !googleClientSecret) {
    console.error('  ❌ Google OAuth credentials missing');
    allConfigured = false;
  } else {
    console.log('  ✅ Google OAuth credentials configured');
  }
  
  if (!twitterClientId || !twitterClientSecret) {
    console.error('  ❌ Twitter OAuth credentials missing');
    allConfigured = false;
  } else {
    console.log('  ✅ Twitter OAuth credentials configured');
  }
  
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    console.error('  ❌ JWT_SECRET missing');
    allConfigured = false;
  } else {
    console.log('  ✅ JWT secret configured');
  }
  
  return allConfigured;
}

async function main() {
  console.log('🚀 Starting Integration Tests\n');
  
  const results = {
    googleAI: await testGoogleAI(),
    twitterAPI: await testTwitterAPI(),
    passportAuth: await testPassportAuth()
  };
  
  console.log('\n📊 Test Results:');
  console.log('  Google AI:', results.googleAI ? '✅ PASS' : '❌ FAIL');
  console.log('  Twitter API:', results.twitterAPI ? '✅ PASS' : '❌ FAIL');
  console.log('  Passport Auth:', results.passportAuth ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All integrations working correctly!');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some integrations need attention. Check the logs above.');
    process.exit(1);
  }
}

// Run tests
main().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
