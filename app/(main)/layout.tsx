import { Metadata } from 'next';
import { redirect } from 'next/navigation';

import { MainNav, MobileNav } from '@/components/layout/main-nav';
import { UserAccountNav } from '@/components/layout/user-account-nav';
import { cookies } from 'next/headers';
import { verify } from 'jsonwebtoken';
import { JWT_SECRET, UserJwtPayload } from '@/lib/auth';

export const metadata: Metadata = {
  title: 'xtasker - Dashboard',
  description: 'AI-Powered Social Media Management',
};

async function getUser() {
  const cookieStore = cookies();
  const token = cookieStore.get('auth_token')?.value;
  
  if (!token) {
    return null;
  }

  try {
    return verify(token, JWT_SECRET) as UserJwtPayload;
  } catch (error) {
    return null;
  }
}

export default async function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4 md:px-6">
          <div className="flex items-center gap-2 font-semibold md:mr-4">
            <span className="text-lg font-semibold hidden md:inline-flex">xtasker</span>
          </div>
          <MainNav />
          <div className="ml-auto flex items-center space-x-4">
            <UserAccountNav
              user={{
                id: user.id,
                name: user.name,
                email: user.email,
                image: user.image,
              }}
            />
          </div>
        </div>
        <MobileNav />
      </header>
      <main className="flex-1 p-4 md:p-6">{children}</main>
    </div>
  );
}