"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  <PERSON>t, 
  Key, 
  Settings, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Loader2,
  ExternalLink,
  Trash2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

const apiKeySchema = z.object({
  provider: z.enum(['OPENAI', 'GOOGLE']),
  apiKey: z.string().min(1, 'API key is required'),
  baseURL: z.string().url().optional().or(z.literal('')),
  organization: z.string().optional(),
});

const modelSelectionSchema = z.object({
  provider: z.enum(['OPENAI', 'GOOGLE']),
  chatModel: z.string().min(1, 'Chat model is required'),
  embeddingModel: z.string().min(1, 'Embedding model is required'),
});

type KeyStatus = {
  openai: { configured: boolean; valid: boolean; error: string | null };
  google: { configured: boolean; valid: boolean; error: string | null };
  currentProvider: string;
};

type ProviderModels = {
  provider: string;
  chat: string[];
  embedding: string[];
  isConfigured: boolean;
  error?: string;
};

type ModelsResponse = {
  providers: ProviderModels[];
  currentSettings: {
    provider: string;
    chatModel: string;
    embeddingModel: string;
  };
};

export default function IntegrationsPage() {
  const [keyStatus, setKeyStatus] = useState<KeyStatus | null>(null);
  const [availableModels, setAvailableModels] = useState<ModelsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const { toast } = useToast();

  const apiKeyForm = useForm<z.infer<typeof apiKeySchema>>({
    resolver: zodResolver(apiKeySchema),
    defaultValues: {
      provider: 'OPENAI',
      apiKey: '',
      baseURL: '',
      organization: '',
    },
  });

  const modelForm = useForm<z.infer<typeof modelSelectionSchema>>({
    resolver: zodResolver(modelSelectionSchema),
    defaultValues: {
      provider: 'OPENAI',
      chatModel: '',
      embeddingModel: '',
    },
  });

  useEffect(() => {
    fetchKeyStatus();
    fetchAvailableModels();
  }, []);

  const fetchKeyStatus = async () => {
    try {
      const response = await fetch('/api/ai/config/keys');
      if (!response.ok) throw new Error('Failed to fetch key status');
      const data = await response.json();
      setKeyStatus(data.keyStatus);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load API key status',
        variant: 'destructive',
      });
    }
  };

  const fetchAvailableModels = async () => {
    setIsLoadingModels(true);
    try {
      const response = await fetch('/api/ai/config/models');
      if (!response.ok) throw new Error('Failed to fetch models');
      const data = await response.json();
      setAvailableModels(data);
      
      // Update model form with current settings
      if (data.currentSettings) {
        modelForm.reset({
          provider: data.currentSettings.provider,
          chatModel: data.currentSettings.chatModel,
          embeddingModel: data.currentSettings.embeddingModel,
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load available models',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingModels(false);
      setIsLoading(false);
    }
  };

  const onSubmitApiKey = async (values: z.infer<typeof apiKeySchema>) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/ai/config/keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save API key');
      }

      toast({
        title: 'Success',
        description: 'API key saved and validated successfully',
      });

      // Refresh status and models
      await fetchKeyStatus();
      await fetchAvailableModels();
      
      // Reset form
      apiKeyForm.reset();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save API key',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSubmitModelSelection = async (values: z.infer<typeof modelSelectionSchema>) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('/api/ai/config/models', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update model preferences');
      }

      toast({
        title: 'Success',
        description: 'Model preferences updated successfully',
      });

      await fetchAvailableModels();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update model preferences',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeApiKey = async (provider: string) => {
    try {
      const response = await fetch(`/api/ai/config/keys?provider=${provider}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to remove API key');

      toast({
        title: 'Success',
        description: `${provider} API key removed successfully`,
      });

      await fetchKeyStatus();
      await fetchAvailableModels();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to remove API key',
        variant: 'destructive',
      });
    }
  };

  const getProviderModels = (provider: string) => {
    return availableModels?.providers.find(p => p.provider === provider);
  };

  const selectedProvider = modelForm.watch('provider');
  const currentProviderModels = getProviderModels(selectedProvider);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">AI Integrations</h1>
        <p className="text-muted-foreground">
          Configure your AI providers and select models for different tasks
        </p>
      </div>

      <Tabs defaultValue="providers" className="space-y-6">
        <TabsList>
          <TabsTrigger value="providers">API Keys</TabsTrigger>
          <TabsTrigger value="models">Model Selection</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-6">
          {/* Provider Status Cards */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* OpenAI Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  OpenAI
                  <Badge variant="outline">
                    <ExternalLink className="h-3 w-3 mr-1" />
                    openai.com
                  </Badge>
                </CardTitle>
                <CardDescription>
                  GPT-4, GPT-3.5 and advanced embedding models
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <div className="flex items-center gap-2">
                    {keyStatus?.openai.configured ? (
                      keyStatus.openai.valid ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Badge variant="default">Connected</Badge>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-500" />
                          <Badge variant="destructive">Invalid Key</Badge>
                        </>
                      )
                    ) : (
                      <>
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        <Badge variant="secondary">Not Configured</Badge>
                      </>
                    )}
                  </div>
                </div>
                {keyStatus?.openai.error && (
                  <p className="text-sm text-red-500">{keyStatus.openai.error}</p>
                )}
                {keyStatus?.openai.configured && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeApiKey('OPENAI')}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove API Key
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Google AI Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  Google AI
                  <Badge variant="outline">
                    <ExternalLink className="h-3 w-3 mr-1" />
                    ai.google.dev
                  </Badge>
                </CardTitle>
                <CardDescription>
                  Gemini models and embedding capabilities
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <div className="flex items-center gap-2">
                    {keyStatus?.google.configured ? (
                      keyStatus.google.valid ? (
                        <>
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <Badge variant="default">Connected</Badge>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-500" />
                          <Badge variant="destructive">Invalid Key</Badge>
                        </>
                      )
                    ) : (
                      <>
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        <Badge variant="secondary">Not Configured</Badge>
                      </>
                    )}
                  </div>
                </div>
                {keyStatus?.google.error && (
                  <p className="text-sm text-red-500">{keyStatus.google.error}</p>
                )}
                {keyStatus?.google.configured && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeApiKey('GOOGLE')}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove API Key
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Add API Key Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Add API Key
              </CardTitle>
              <CardDescription>
                Configure your AI provider API keys to enable model access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...apiKeyForm}>
                <form onSubmit={apiKeyForm.handleSubmit(onSubmitApiKey)} className="space-y-4">
                  <FormField
                    control={apiKeyForm.control}
                    name="provider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Provider</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="OPENAI">OpenAI</SelectItem>
                            <SelectItem value="GOOGLE">Google AI</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={apiKeyForm.control}
                    name="apiKey"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>API Key</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Enter your API key" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Your API key will be encrypted and stored securely
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {apiKeyForm.watch('provider') === 'OPENAI' && (
                    <>
                      <FormField
                        control={apiKeyForm.control}
                        name="baseURL"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Base URL (Optional)</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="https://api.openai.com/v1" 
                                {...field} 
                              />
                            </FormControl>
                            <FormDescription>
                              Custom base URL for OpenAI-compatible APIs
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={apiKeyForm.control}
                        name="organization"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Organization ID (Optional)</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="org-xxxxxxxxxxxxxxxxxxxxxxxx" 
                                {...field} 
                              />
                            </FormControl>
                            <FormDescription>
                              Your OpenAI organization ID
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Validating...
                      </>
                    ) : (
                      <>
                        <Key className="mr-2 h-4 w-4" />
                        Save API Key
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Model Selection
              </CardTitle>
              <CardDescription>
                Choose your preferred models for different AI tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingModels ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading available models...</span>
                </div>
              ) : (
                <Form {...modelForm}>
                  <form onSubmit={modelForm.handleSubmit(onSubmitModelSelection)} className="space-y-4">
                    <FormField
                      control={modelForm.control}
                      name="provider"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>AI Provider</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a provider" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableModels?.providers
                                .filter(p => p.isConfigured)
                                .map(provider => (
                                  <SelectItem key={provider.provider} value={provider.provider}>
                                    {provider.provider === 'OPENAI' ? 'OpenAI' : 'Google AI'}
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {currentProviderModels && (
                      <>
                        <FormField
                          control={modelForm.control}
                          name="chatModel"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Chat/LLM Model</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a chat model" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {currentProviderModels.chat.map(model => (
                                    <SelectItem key={model} value={model}>
                                      {model}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Model used for chat completions and content generation
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={modelForm.control}
                          name="embeddingModel"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Embedding Model</FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select an embedding model" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {currentProviderModels.embedding.map(model => (
                                    <SelectItem key={model} value={model}>
                                      {model}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                Model used for semantic search and embeddings
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {currentProviderModels.error && (
                          <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                            <p className="text-sm text-red-600">
                              <AlertCircle className="h-4 w-4 inline mr-2" />
                              {currentProviderModels.error}
                            </p>
                          </div>
                        )}
                      </>
                    )}

                    {!currentProviderModels?.isConfigured && (
                      <div className="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                        <p className="text-sm text-yellow-600">
                          <AlertCircle className="h-4 w-4 inline mr-2" />
                          Please configure an API key for {selectedProvider} first
                        </p>
                      </div>
                    )}

                    <Button 
                      type="submit" 
                      disabled={isSubmitting || !currentProviderModels?.isConfigured}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Settings className="mr-2 h-4 w-4" />
                          Update Model Preferences
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
