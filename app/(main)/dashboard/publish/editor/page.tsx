"use client";

import { useState } from "react";
import { useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Calendar, Image, CloudLightning as Lightning, Notebook as Robot, Send, <PERSON>rk<PERSON>, Clock } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TweetPreview } from "@/components/tweet/tweet-preview";
import { ScheduleSelector } from "@/components/tweet/schedule-selector";

const formSchema = z.object({
  content: z
    .string()
    .min(1, { message: "Tweet content is required" })
    .max(280, { message: "Tweet cannot exceed 280 characters" }),
  twitterAccountId: z.string().min(1, { message: "Twitter account is required" }),
  botId: z.string().optional(),
  aiPrompt: z.string().optional(),
  scheduledFor: z.date().optional(),
  media: z.any().optional(), // For file uploads
});

const mockBots = [
  { id: "1", name: "Professional Marketer" },
  { id: "2", name: "Casual Conversationalist" },
  { id: "3", name: "Tech Enthusiast" },
  { id: "4", name: "Motivational Coach" },
];

const mockTwitterAccounts = [
  { id: "twitter-1", name: "@YourMainAccount" },
  { id: "twitter-2", name: "@YourBrandAccount" },
  { id: "twitter-3", name: "@PersonalHandle" },
];

export default function EditorPage() {
  const searchParams = useSearchParams();
  const botId = searchParams.get("bot");
  
  const [tweetContent, setTweetContent] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSchedule, setShowSchedule] = useState(false);
  const [mediaPreview, setMediaPreview] = useState<string[]>([]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "", // X post content
      twitterAccountId: mockTwitterAccounts[0].id, // Default to the first account
      botId: botId || "",
      aiPrompt: "",
      scheduledFor: undefined,
      media: undefined,
    },
  });

  const characterCount = form.watch("content").length;
  const selectedBot = form.watch("botId");

  const handleGenerate = async () => {
    setIsGenerating(true);
    
    // Mock generation delay
    setTimeout(() => {
      const aiPrompt = form.getValues("aiPrompt");
      const botId = form.getValues("botId");
      
      // Mock AI-generated content
      let generatedContent = "";
      
      if (botId === "1") {
        generatedContent = "Excited to announce our latest feature! Boost your productivity with our new AI-powered assistant. Try it now and experience the difference. #ProductLaunch #Innovation";
      } else if (botId === "2") {
        generatedContent = "Hey folks! Just dropped an awesome new feature - our AI assistant is here to make your life easier! Check it out and let me know what you think 😊 #NewFeature";
      } else if (botId === "3") {
        generatedContent = "NEW TECH ALERT: Our AI assistant leverages transformer architecture with 175B parameters to deliver contextually relevant responses in real-time. The future is now! #TechNews #AI";
      } else if (botId === "4") {
        generatedContent = "Your journey to success begins with a single step. Our new AI assistant is here to guide you on that journey. Embrace the possibilities and unlock your full potential! #Motivation #Growth";
      } else {
        generatedContent = "Check out our latest feature! Our new AI assistant is now available to help you with your tasks. #NewFeature #AI";
      }
      
      form.setValue("content", generatedContent);
      setTweetContent(generatedContent);
      setIsGenerating(false);
    }, 1500);
  };

  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log("Tweet data:", data);
    // Here would be the API call to save/publish the tweet
    
    // Mock success message
    alert(data.scheduledFor 
      ? `Tweet scheduled for ${data.scheduledFor.toLocaleString()}` 
      : "Tweet published successfully!"
    );
    
    // Reset form
    form.reset();
    setTweetContent("");
    setShowSchedule(false);
    setMediaPreview([]);
  };

  const handleMediaChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      form.setValue("media", files);
      const previews = files.map(file => URL.createObjectURL(file));
      setMediaPreview(previews);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Compose Tweet</h1>
        <p className="text-muted-foreground">
          Create new content with AI assistance or from scratch
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tweet Editor</CardTitle>
              <CardDescription>
                Compose your tweet or generate content with AI
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tweet Content</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="What's happening?"
                            className="min-h-[120px] resize-none"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              setTweetContent(e.target.value);
                            }}
                          />
                        </FormControl>
                        <div className="flex justify-between">
                          <FormMessage />
                          <p className={`text-xs ${characterCount > 280 ? "text-destructive" : "text-muted-foreground"}`}>
                            {characterCount}/280
                          </p>
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="twitterAccountId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Twitter Account</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a Twitter account" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {mockTwitterAccounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose the Twitter account to publish from.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                   <div className="flex flex-wrap gap-2">
                     <Button
                       type="button"
                       variant="outline"
                       size="sm"
                       onClick={() => setShowSchedule(!showSchedule)}
                     >
                       <Clock className="mr-2 h-4 w-4" />
                       Schedule
                     </Button>
                     <FormField
                       control={form.control}
                       name="media"
                       render={({ field }) => (
                         <FormItem>
                           <FormControl>
                             <Button
                               type="button"
                               variant="outline"
                               size="sm"
                               onClick={() => document.getElementById('media-upload-input')?.click()}
                             >
                               <Image className="mr-2 h-4 w-4" />
                               Add Media
                             </Button>
                           </FormControl>
                           <Input
                             id="media-upload-input"
                             type="file"
                             multiple
                             accept="image/*,video/*"
                             className="hidden"
                             onChange={handleMediaChange}
                           />
                           <FormMessage />
                         </FormItem>
                       )}
                     />
                   </div>
                   
                   {mediaPreview.length > 0 && (
                     <div className="mt-4 grid grid-cols-2 gap-2">
                       {mediaPreview.map((src, index) => (
                         <div key={index} className="relative">
                           {src.startsWith('blob:video') ? (
                             <video src={src} controls className="w-full h-auto rounded-md object-cover" />
                           ) : (
                             <img src={src} alt={`Media preview ${index + 1}`} className="w-full h-auto rounded-md object-cover" />
                           )}
                         </div>
                       ))}
                     </div>
                   )}

                   {showSchedule && (
                     <div className="pt-4">
                       <FormField
                         control={form.control}
                         name="scheduledFor"
                         render={({ field }) => (
                           <FormItem>
                             <FormLabel>Schedule For</FormLabel>
                             <ScheduleSelector value={field.value} onChange={field.onChange} />
                             <FormDescription>
                               Select date and time for publishing this tweet
                             </FormDescription>
                           </FormItem>
                         )}
                       />
                     </div>
                   )}
                   
                   <div className="flex justify-end">
                     <Button
                       type="submit"
                       disabled={characterCount === 0 || characterCount > 280}
                     >
                       {showSchedule ? (
                         <>
                           <Calendar className="mr-2 h-4 w-4" />
                           Schedule Tweet
                         </>
                       ) : (
                         <>
                           <Send className="mr-2 h-4 w-4" />
                           Publish Now
                         </>
                       )}
                     </Button>
                   </div>
                 </form>
               </Form>
             </CardContent>
           </Card>
           
           <Card>
             <CardHeader>
               <CardTitle>AI Assistance</CardTitle>
               <CardDescription>
                 Generate tweet content using AI with your bot personas
               </CardDescription>
             </CardHeader>
             <CardContent>
               <div className="space-y-4">
                 <div className="space-y-2">
                   <FormLabel>Bot Persona</FormLabel>
                   <Select
                     value={form.getValues("botId") || ""}
                     onValueChange={value => form.setValue("botId", value)}
                   >
                     <SelectTrigger>
                       <SelectValue placeholder="Select a bot persona" />
                     </SelectTrigger>
                     <SelectContent>
                       {mockBots.map(bot => (
                         <SelectItem key={bot.id} value={bot.id}>
                           {bot.name}
                         </SelectItem>
                       ))}
                     </SelectContent>
                   </Select>
                 </div>
                 
                 <div className="space-y-2">
                   <FormLabel>Generation Prompt</FormLabel>
                   <Textarea
                     placeholder="E.g., Write a tweet about our new product launch"
                     value={form.getValues("aiPrompt") || ""}
                     onChange={e => form.setValue("aiPrompt", e.target.value)}
                     className="min-h-[80px] resize-none"
                   />
                 </div>
                 
                 <Button
                   type="button"
                   onClick={handleGenerate}
                   disabled={isGenerating || !selectedBot}
                   className="w-full"
                 >
                   {isGenerating ? (
                     <>
                       <Lightning className="mr-2 h-4 w-4 animate-pulse" />
                       Generating...
                     </>
                   ) : (
                     <>
                       <Sparkles className="mr-2 h-4 w-4" />
                       Generate Tweet
                     </>
                   )}
                 </Button>
               </div>
             </CardContent>
           </Card>
         </div>
         
         <div className="space-y-6">
           <Card>
             <CardHeader>
               <CardTitle>Preview</CardTitle>
               <CardDescription>
                 See how your tweet will look
               </CardDescription>
             </CardHeader>
             <CardContent>
               <TweetPreview content={tweetContent} />
             </CardContent>
           </Card>
           
           <Card>
             <CardHeader>
               <CardTitle>Quick Tips</CardTitle>
               <CardDescription>
                 Improve your tweet engagement
               </CardDescription>
             </CardHeader>
             <CardContent>
               <div className="space-y-4">
                 <div className="flex items-start gap-3">
                   <div className="rounded-full bg-primary/10 p-2">
                     <Lightning className="h-4 w-4 text-primary" />
                   </div>
                   <div>
                     <p className="font-medium">Use relevant hashtags</p>
                     <p className="text-sm text-muted-foreground">
                       Add 1-2 relevant hashtags to increase discoverability
                     </p>
                   </div>
                 </div>
                 
                 <div className="flex items-start gap-3">
                   <div className="rounded-full bg-primary/10 p-2">
                     <Lightning className="h-4 w-4 text-primary" />
                   </div>
                   <div>
                     <p className="font-medium">Include a call to action</p>
                     <p className="text-sm text-muted-foreground">
                       Ask questions or prompt users to engage
                     </p>
                   </div>
                 </div>
                 
                 <div className="flex items-start gap-3">
                   <div className="rounded-full bg-primary/10 p-2">
                     <Lightning className="h-4 w-4 text-primary" />
                   </div>
                   <div>
                     <p className="font-medium">Add visual elements</p>
                     <p className="text-sm text-muted-foreground">
                       Tweets with images get 150% more retweets
                     </p>
                   </div>
                 </div>
                 
                 <div className="flex items-start gap-3">
                   <div className="rounded-full bg-primary/10 p-2">
                     <Lightning className="h-4 w-4 text-primary" />
                   </div>
                   <div>
                     <p className="font-medium">Keep it concise</p>
                     <p className="text-sm text-muted-foreground">
                       Shorter tweets often perform better than longer ones
                     </p>
                   </div>
                 </div>
               </div>
             </CardContent>
           </Card>
         </div>
       </div>
     </div>
   );
 }