"use client";

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { User, Settings, Twitter, Bot, Calendar, Brain, Save, Loader2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { getInitials } from '@/lib/utils';

const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  profilePicture: z.string().url('Must be a valid URL').optional().or(z.literal('')),
});

type ProfileData = {
  id: string;
  email: string;
  name: string;
  profilePicture: string | null;
  createdAt: string;
  updatedAt: string;
  aiProvider: string;
  chatModel: string;
  embeddingModel: string;
  connectedAccounts: {
    twitter: Array<{
      id: string;
      username: string;
      displayName: string;
      profileImageUrl: string | null;
      isActive: boolean;
    }>;
  };
  stats: {
    botPersonas: number;
    scheduledTweets: number;
    brainEntries: number;
  };
};

export default function ProfilePage() {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  const form = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      profilePicture: '',
    },
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (!response.ok) {
        throw new Error('Failed to fetch profile');
      }
      const data = await response.json();
      setProfile(data.user);
      
      // Update form with current values
      form.reset({
        name: data.user.name || '',
        profilePicture: data.user.profilePicture || '',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load profile data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof profileSchema>) => {
    setIsUpdating(true);
    try {
      const response = await fetch('/api/auth/me', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update profile');
      }

      const data = await response.json();
      setProfile(prev => prev ? { ...prev, ...data.user } : null);
      
      toast({
        title: 'Success',
        description: 'Profile updated successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load profile data</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Profile</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Information
            </CardTitle>
            <CardDescription>
              Update your personal information and profile picture
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={profile.profilePicture || undefined} />
                <AvatarFallback className="text-lg">
                  {getInitials(profile.name || profile.email)}
                </AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-semibold">{profile.name || 'No name set'}</h3>
                <p className="text-sm text-muted-foreground">{profile.email}</p>
                <p className="text-xs text-muted-foreground">
                  Member since {new Date(profile.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Display Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="profilePicture"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Profile Picture URL</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/avatar.jpg" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" disabled={isUpdating}>
                  {isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Account Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Account Overview
            </CardTitle>
            <CardDescription>
              Your account statistics and connected services
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* AI Settings */}
            <div>
              <h4 className="font-medium mb-2">AI Configuration</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Provider:</span>
                  <Badge variant="secondary">{profile.aiProvider}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Chat Model:</span>
                  <span className="text-sm">{profile.chatModel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Embedding Model:</span>
                  <span className="text-sm">{profile.embeddingModel}</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Statistics */}
            <div>
              <h4 className="font-medium mb-2">Usage Statistics</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <Bot className="h-6 w-6 mx-auto mb-1 text-blue-500" />
                  <div className="text-2xl font-bold">{profile.stats.botPersonas}</div>
                  <div className="text-xs text-muted-foreground">Bot Personas</div>
                </div>
                <div className="text-center">
                  <Calendar className="h-6 w-6 mx-auto mb-1 text-green-500" />
                  <div className="text-2xl font-bold">{profile.stats.scheduledTweets}</div>
                  <div className="text-xs text-muted-foreground">Scheduled</div>
                </div>
                <div className="text-center">
                  <Brain className="h-6 w-6 mx-auto mb-1 text-purple-500" />
                  <div className="text-2xl font-bold">{profile.stats.brainEntries}</div>
                  <div className="text-xs text-muted-foreground">Brain Entries</div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Connected Accounts */}
            <div>
              <h4 className="font-medium mb-2">Connected Accounts</h4>
              {profile.connectedAccounts.twitter.length > 0 ? (
                <div className="space-y-2">
                  {profile.connectedAccounts.twitter.map((account) => (
                    <div key={account.id} className="flex items-center gap-3 p-2 border rounded-lg">
                      <Twitter className="h-4 w-4 text-blue-500" />
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={account.profileImageUrl || undefined} />
                        <AvatarFallback className="text-xs">
                          {getInitials(account.displayName)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{account.displayName}</p>
                        <p className="text-xs text-muted-foreground">@{account.username}</p>
                      </div>
                      <Badge variant={account.isActive ? "default" : "secondary"}>
                        {account.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No Twitter accounts connected</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
