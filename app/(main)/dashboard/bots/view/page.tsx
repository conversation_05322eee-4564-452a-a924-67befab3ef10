import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { BotList } from "@/components/bots/bot-list";

export const metadata: Metadata = {
  title: "Bot Personas - xtasker",
  description: "Manage your AI bot personas",
};

export default function BotsPage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Bot Personas</h1>
          <p className="text-muted-foreground">
            Create and manage AI personas for different content styles
          </p>
        </div>
        <Link href="/dashboard/bots/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Bot
          </Button>
        </Link>
      </div>
      <BotList />
    </div>
  );
}