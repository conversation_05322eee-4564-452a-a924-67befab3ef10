"use client";

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Send, Bot, User, Loader2, AlertCircle, Settings } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';

interface Message {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface BotInfo {
  id: string;
  name: string;
  description?: string;
  provider: string;
  chatModel?: string;
  persona: {
    name: string;
    tone: string;
    writingStyle: string;
    traits: string[];
  };
}

interface ChatConfiguration {
  isConfigured: boolean;
  provider: string;
  model?: string;
}

export default function BotChatPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [bot, setBot] = useState<BotInfo | null>(null);
  const [configuration, setConfiguration] = useState<ChatConfiguration | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchBotInfo();
  }, [params.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchBotInfo = async () => {
    try {
      const response = await fetch(`/api/bots/${params.id}/chat`);
      if (!response.ok) {
        if (response.status === 404) {
          toast({
            title: 'Error',
            description: 'Bot not found',
            variant: 'destructive',
          });
          router.push('/dashboard/bots/view');
          return;
        }
        throw new Error('Failed to fetch bot info');
      }
      
      const data = await response.json();
      setBot(data.bot);
      setConfiguration(data.configuration);
      
      // Add welcome message
      if (data.bot) {
        setMessages([{
          role: 'assistant',
          content: `Hello! I'm ${data.bot.name}. ${data.bot.description || 'How can I help you today?'}`,
          timestamp: new Date().toISOString()
        }]);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load bot information',
        variant: 'destructive',
      });
      router.push('/dashboard/bots/view');
    } finally {
      setIsLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isSending || !configuration?.isConfigured) return;

    const userMessage: Message = {
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString()
    };

    // Add user message to chat
    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsSending(true);

    try {
      const response = await fetch(`/api/bots/${params.id}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage.content,
          conversationHistory: messages
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to send message');
      }

      const data = await response.json();
      
      // Add bot response to chat
      setMessages(prev => [...prev, data.response]);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send message',
        variant: 'destructive',
      });
      
      // Remove the user message if sending failed
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case 'OPENAI':
        return 'bg-green-100 text-green-800';
      case 'GOOGLE':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2 text-muted-foreground">Loading chat...</span>
      </div>
    );
  }

  if (!bot || !configuration) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold">Bot not found</h3>
        <p className="text-muted-foreground">The bot you're looking for doesn't exist.</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/bots/view">Back to Bots</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/dashboard/bots/view">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                <AvatarFallback>
                  <Bot className="h-5 w-5" />
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-lg font-semibold">{bot.name}</h1>
                <div className="flex items-center gap-2">
                  <Badge className={getProviderBadgeColor(bot.provider)}>
                    {bot.provider}
                  </Badge>
                  {bot.chatModel && (
                    <Badge variant="outline" className="text-xs">
                      {bot.chatModel}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {!configuration.isConfigured && (
            <Button variant="outline" asChild>
              <Link href="/dashboard/integrations">
                <Settings className="h-4 w-4 mr-2" />
                Configure AI
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* Configuration Warning */}
      {!configuration.isConfigured && (
        <div className="p-4 bg-yellow-50 border-b border-yellow-200">
          <div className="flex items-center gap-2 text-yellow-800">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">
              {configuration.provider} API key not configured. 
              <Link href="/dashboard/integrations" className="underline ml-1">
                Configure it here
              </Link>
            </span>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role === 'assistant' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                    <AvatarFallback>
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={`max-w-[70%] rounded-lg px-4 py-2 ${
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.role === 'user' 
                      ? 'text-primary-foreground/70' 
                      : 'text-muted-foreground'
                  }`}>
                    {formatTime(message.timestamp)}
                  </p>
                </div>

                {message.role === 'user' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback>
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {isSending && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                  <AvatarFallback>
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-4 py-2">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>
      </div>

      {/* Input */}
      <div className="border-t p-4">
        <div className="flex gap-2">
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={
              configuration.isConfigured 
                ? "Type your message..." 
                : "Configure AI provider to start chatting"
            }
            disabled={!configuration.isConfigured || isSending}
            className="flex-1"
          />
          <Button 
            onClick={sendMessage}
            disabled={!inputMessage.trim() || !configuration.isConfigured || isSending}
            size="icon"
          >
            {isSending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-2">
          Press Enter to send, Shift+Enter for new line
        </p>
      </div>
    </div>
  );
}
