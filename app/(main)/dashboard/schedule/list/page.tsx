"use client";

import { useState } from "react";
import Link from "next/link";
import { Plus, Edit, Trash2, Calendar, ListFilter, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { TweetPreview } from "@/components/tweet/tweet-preview";
import { truncateText } from "@/lib/utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Mock data
const mockScheduledTweets = [
  {
    id: "1",
    content: "Exciting news! We're launching our new AI feature next week. Stay tuned for updates! #ProductLaunch #AI",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // 2 days from now
    botPersona: { id: "1", name: "Professional Marketer" },
    hasMedia: false,
    status: "pending",
  },
  {
    id: "2",
    content: "Did you know that our platform can help you increase your engagement by up to 40%? Try our free trial today! #SocialMedia #Marketing",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 3), // 3 days from now
    botPersona: { id: "2", name: "Casual Conversationalist" },
    hasMedia: true,
    status: "pending",
  },
  {
    id: "3",
    content: "Join our webinar next Thursday to learn about the latest trends in social media marketing and how to leverage them for your business. Register now at example.com/webinar #Webinar #SocialMedia",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // 5 days from now
    botPersona: { id: "4", name: "Motivational Coach" },
    hasMedia: false,
    status: "pending",
  },
  {
    id: "4",
    content: "We're excited to announce our partnership with @TechCorp to bring you even more powerful AI features. #Partnership #Innovation",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days from now
    botPersona: { id: "1", name: "Professional Marketer" },
    hasMedia: true,
    status: "pending",
  },
  {
    id: "5",
    content: "Happy Friday! What are your plans for the weekend? We'll be working on some exciting new features for you all! #FridayFeeling",
    scheduledFor: new Date(Date.now() + 1000 * 60 * 60 * 24 * 9), // 9 days from now
    botPersona: { id: "2", name: "Casual Conversationalist" },
    hasMedia: false,
    status: "pending",
  },
  {
    id: "6",
    content: "A tweet that was published successfully. #Success",
    scheduledFor: new Date(Date.now() - 1000 * 60 * 60 * 24 * 1), // 1 day ago
    botPersona: { id: "1", name: "Professional Marketer" },
    hasMedia: false,
    status: "published",
  },
  {
    id: "7",
    content: "A tweet that failed to publish due to an error. #Error",
    scheduledFor: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
    botPersona: { id: "3", name: "Tech Enthusiast" },
    hasMedia: true,
    status: "failed",
  },
];

export default function SchedulePage() {
  const [scheduledTweets, setScheduledTweets] = useState(mockScheduledTweets);
  const [selectedTweet, setSelectedTweet] = useState<string | null>(null);
  const [tweetToDelete, setTweetToDelete] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const tweetsPerPage = 5;

  const filteredTweets = scheduledTweets.filter(tweet =>
    filterStatus === "all" || tweet.status === filterStatus
  );

  const totalPages = Math.ceil(filteredTweets.length / tweetsPerPage);
  const startIndex = (currentPage - 1) * tweetsPerPage;
  const endIndex = startIndex + tweetsPerPage;
  const currentTweets = filteredTweets.slice(startIndex, endIndex);

  const handleDeleteTweet = () => {
    if (tweetToDelete) {
      setScheduledTweets(scheduledTweets.filter(tweet => tweet.id !== tweetToDelete));
      setTweetToDelete(null);
      if (selectedTweet === tweetToDelete) {
        setSelectedTweet(null);
      }
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const selectedTweetData = scheduledTweets.find(tweet => tweet.id === selectedTweet);

  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Scheduled Tweets</h1>
            <p className="text-muted-foreground">
              Manage your upcoming scheduled tweets
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <ListFilter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            <Link href="/dashboard/publish/editor">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Tweet
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Tweets</CardTitle>
                <CardDescription>
                  Your scheduled posts for the coming days
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[150px] md:w-[180px]">Scheduled For</TableHead>
                      <TableHead className="hidden md:table-cell w-[30%]">Content</TableHead>
                      <TableHead className="hidden lg:table-cell">Bot</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentTweets.map((tweet) => (
                      <TableRow
                        key={tweet.id}
                        onClick={() => setSelectedTweet(tweet.id)}
                        className={selectedTweet === tweet.id ? "bg-muted/50 cursor-pointer" : "cursor-pointer hover:bg-muted/30"}
                      >
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{format(tweet.scheduledFor, 'MMM d, yyyy')}</span>
                            <span className="text-xs text-muted-foreground">
                              {format(tweet.scheduledFor, 'h:mm a')}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="flex items-center">
                            <span className="line-clamp-1">{truncateText(tweet.content, 60)}</span>
                            {tweet.hasMedia && (
                              <Badge variant="outline" className="ml-2">
                                Media
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          {tweet.botPersona ? (
                            <Badge variant="secondary">
                              {tweet.botPersona.name}
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground text-sm">None</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              tweet.status === "published" ? "default" :
                              tweet.status === "failed" ? "destructive" :
                              "outline"
                            }
                          >
                            {tweet.status.charAt(0).toUpperCase() + tweet.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" asChild onClick={(e) => e.stopPropagation()}>
                              <Link href={`/dashboard/publish/editor?edit=${tweet.id}`}>
                                <Edit className="h-4 w-4" />
                                <span className="sr-only">Edit</span>
                              </Link>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                setTweetToDelete(tweet.id);
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                    {currentTweets.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          <div className="flex flex-col items-center justify-center gap-2">
                            <Calendar className="h-8 w-8 text-muted-foreground" />
                            <div>No scheduled tweets</div>
                            <Link href="/dashboard/publish/editor">
                              <Button variant="outline" size="sm">
                                Create one
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
            <div className="flex justify-center mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (currentPage > 1) handlePageChange(currentPage - 1);
                      }}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                    />
                  </PaginationItem>
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handlePageChange(page);
                        }}
                        isActive={page === currentPage}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}
                  <PaginationItem>
                    <PaginationNext
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (currentPage < totalPages) handlePageChange(currentPage + 1);
                      }}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : undefined}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Tweet Preview</CardTitle>
              <CardDescription>
                {selectedTweetData
                  ? `Scheduled for ${format(selectedTweetData.scheduledFor, 'MMM d, yyyy')} at ${format(selectedTweetData.scheduledFor, 'h:mm a')}`
                  : "Select a tweet to preview"
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedTweetData ? (
                <TweetPreview
                  content={selectedTweetData.content}
                  media={selectedTweetData.hasMedia ? ["https://via.placeholder.com/300/0000FF/FFFFFF?text=Media+Preview"] : []} // Mock media URL
                />
              ) : (
                <div className="flex h-[200px] items-center justify-center rounded-md border border-dashed">
                  <div className="flex flex-col items-center gap-1 text-center">
                    <Calendar className="h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Select a scheduled tweet to preview
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
            {selectedTweetData && (
              <CardFooter className="border-t bg-muted/50 flex justify-between">
                <Link href={`/dashboard/publish/editor?edit=${selectedTweetData.id}`}>
                  <Button variant="outline">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  onClick={() => setTweetToDelete(selectedTweetData.id)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      <AlertDialog open={!!tweetToDelete} onOpenChange={() => setTweetToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this scheduled tweet.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteTweet} 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}