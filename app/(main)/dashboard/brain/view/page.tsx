import { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { BrainEntryList } from "@/components/brain/brain-entry-list";
import { BrainSearch } from "@/components/brain/brain-search";

export const metadata: Metadata = {
  title: "Knowledge Base - xtasker",
  description: "Manage your knowledge base entries",
};

export default function KnowledgeBasePage() {
  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Knowledge Base</h1>
          <p className="text-muted-foreground">
            Store and organize your content ideas and references
          </p>
        </div>
        <Link href="/dashboard/brain/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Entry
          </Button>
        </Link>
      </div>
      
      <BrainSearch />
      <BrainEntryList />
    </div>
  );
}