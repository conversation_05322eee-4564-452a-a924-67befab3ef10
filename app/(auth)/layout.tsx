import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="flex h-16 items-center border-b px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2 font-semibold">
          <ArrowRight className="h-6 w-6" />
          <span className="text-lg font-semibold">xtasker</span>
        </Link>
      </header>
      <main className="flex flex-1 items-center justify-center p-6 md:p-8">
        <div className="mx-auto grid w-full max-w-[1200px] gap-6 md:grid-cols-2 md:gap-12">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">
                AI-Powered X Management
              </h1>
              <p className="text-gray-500 dark:text-gray-400">
                Create, schedule, and publish content intelligently with our AI-powered tools.
                Streamline your workflow and boost your X presence.
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Smart Content Generation</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                <span className="text-sm text-gray-500 dark:text-gray-400">Seamless Publishing</span>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-center">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}