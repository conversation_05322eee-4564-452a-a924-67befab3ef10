import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'xtasker - AI-Powered Twitter/X Management',
  description: 'Comprehensive AI-assisted Twitter/X management platform for composing, scheduling, and publishing tweets with intelligent bot personas and semantic knowledge base.',
  keywords: ['Twitter', 'X', 'AI', 'Social Media', 'Management', 'Automation', 'Bot', 'Scheduling'],
  authors: [{ name: 'xtasker Team' }],
  creator: 'xtasker',
  publisher: 'xtasker',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
