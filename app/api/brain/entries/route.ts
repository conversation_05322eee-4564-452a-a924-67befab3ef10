import { NextRequest } from "next/server";
import { Prisma } from "@prisma/client";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { validateAuth } from "@/lib/middleware/auth";
import { BrainEntryCreate } from "@/lib/brain/types";
import { EmbeddingService } from "@/lib/brain/embeddings";

const entryCreateSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1).max(10000),
  sourceUrl: z.string().url().optional(),
  categoryId: z.string().optional(),
});

const listQuerySchema = z.object({
  categoryId: z.string().optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional(),
});

export async function GET(req: NextRequest) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = listQuerySchema.safeParse(Object.fromEntries(searchParams));

    if (!query.success) {
      return new Response("Invalid query parameters", { status: 400 });
    }

    const { categoryId, limit = 50, offset = 0 } = query.data;

    const categoryFilter = categoryId 
      ? Prisma.sql`AND e."categoryId" = ${categoryId}` 
      : Prisma.empty;

    const entries = await prisma.$queryRaw<Array<any>>`
      SELECT e.*, c.*
      FROM "BrainEntry" e
      LEFT JOIN "BrainCategory" c ON e."categoryId" = c.id
      WHERE e."userId" = ${auth.id}
      ${categoryFilter}
      ORDER BY e."createdAt" DESC
      LIMIT ${limit}
      OFFSET ${offset}
    `;

    const [{ count }] = await prisma.$queryRaw<[{ count: number }]>`
      SELECT COUNT(*) as count
      FROM "BrainEntry"
      WHERE "userId" = ${auth.id}
      ${categoryFilter}
    `;

    return Response.json({
      entries,
      total: Number(count),
      limit,
      offset,
    });
  } catch (error) {
    console.error("Failed to fetch entries:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const json = await req.json();
    const parsed = entryCreateSchema.safeParse(json);

    if (!parsed.success) {
      return new Response("Invalid request body", { status: 400 });
    }

    // If categoryId is provided, verify it exists and user has access
    if (parsed.data.categoryId) {
      const category = await prisma.brainCategory.findFirst({
        where: {
          id: parsed.data.categoryId,
          userId: auth.id,
        },
      });

      if (!category) {
        return new Response("Invalid category", { status: 400 });
      }
    }

    // Get user to initialize embedding service
    const user = await prisma.user.findUnique({
      where: { id: auth.id },
    });

    if (!user) {
      throw new Error("User not found");
    }

    // Create entry without embedding first
    const [newEntry] = await prisma.$queryRaw<[any]>`
      INSERT INTO "BrainEntry" (
        id,
        "userId",
        title,
        content,
        "sourceUrl",
        "categoryId",
        "createdAt",
        "updatedAt"
      )
      VALUES (
        gen_random_uuid(),
        ${auth.id},
        ${parsed.data.title},
        ${parsed.data.content},
        ${parsed.data.sourceUrl || null},
        ${parsed.data.categoryId || null},
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
      )
      RETURNING *
    `;

    // Generate and store embedding
    const embeddingService = new EmbeddingService(user);
    const updatedEntry = await embeddingService.storeEntry(newEntry);

    return Response.json(updatedEntry);
  } catch (error) {
    console.error("Failed to create entry:", error);
    if (error instanceof Error) {
      return new Response(error.message, { status: 500 });
    }
    return new Response("Internal Server Error", { status: 500 });
  }
}