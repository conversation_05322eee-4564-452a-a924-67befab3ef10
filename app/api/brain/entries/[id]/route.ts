import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { validateAuth } from "@/lib/middleware/auth";
import { BrainEntryUpdate } from "@/lib/brain/types";
import { EmbeddingService } from "@/lib/brain/embeddings";

const entryUpdateSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().min(1).max(10000).optional(),
  sourceUrl: z.string().url().optional(),
  categoryId: z.string().optional(),
});

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const entry = await prisma.brainEntry.findUnique({
      where: {
        id: params.id,
      },
      include: {
        category: true,
      },
    });

    if (!entry) {
      return new Response("Entry not found", { status: 404 });
    }

    if (entry.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    return Response.json(entry);
  } catch (error) {
    console.error("Failed to fetch entry:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const entry = await prisma.brainEntry.findUnique({
      where: { id: params.id },
    });

    if (!entry) {
      return new Response("Entry not found", { status: 404 });
    }

    if (entry.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    const json = await req.json();
    const parsed = entryUpdateSchema.safeParse(json);

    if (!parsed.success) {
      return new Response("Invalid request body", { status: 400 });
    }

    // If categoryId is provided, verify it exists and user has access
    if (parsed.data.categoryId) {
      const category = await prisma.brainCategory.findFirst({
        where: {
          id: parsed.data.categoryId,
          userId: auth.id,
        },
      });

      if (!category) {
        return new Response("Invalid category", { status: 400 });
      }
    }

    const update: BrainEntryUpdate = {
      id: params.id,
      ...parsed.data,
    };

    // If content is updated, regenerate embedding
    if (parsed.data.content) {
      const user = await prisma.user.findUnique({ where: { id: auth.id } });
      if (!user) {
        throw new Error("User not found");
      }

      const embeddingService = new EmbeddingService(user);
      const updatedEntry = await embeddingService.storeEntry({
        ...entry,
        content: parsed.data.content,
      });

      return Response.json(updatedEntry);
    }

    // Otherwise just update the other fields
    const updated = await prisma.$executeRaw`
      UPDATE "BrainEntry"
      SET
        title = COALESCE(${update.title}, title),
        source_url = COALESCE(${update.sourceUrl}, source_url),
        category_id = COALESCE(${update.categoryId}, category_id),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${params.id}
      RETURNING *
    `;

    return Response.json(updated);
  } catch (error) {
    console.error("Failed to update entry:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const entry = await prisma.brainEntry.findUnique({
      where: { id: params.id },
    });

    if (!entry) {
      return new Response("Entry not found", { status: 404 });
    }

    if (entry.userId !== auth.id) {
      return new Response("Unauthorized", { status: 403 });
    }

    await prisma.brainEntry.delete({
      where: { id: params.id },
    });

    return new Response(null, { status: 204 });
  } catch (error) {
    console.error("Failed to delete entry:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}