import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { validateAuth } from "@/lib/middleware/auth";
import { EmbeddingService } from "@/lib/brain/embeddings";

const searchQuerySchema = z.object({
  query: z.string().min(1).max(1000),
  categoryId: z.string().optional(),
  limit: z.string().transform(Number).optional(),
});

const DEFAULT_SEARCH_LIMIT = 20;

export async function GET(req: NextRequest) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = searchQuerySchema.safeParse(Object.fromEntries(searchParams));

    if (!query.success) {
      return new Response("Invalid query parameters", { status: 400 });
    }

    const { query: searchQuery, categoryId, limit = DEFAULT_SEARCH_LIMIT } = query.data;

    // Get user to initialize embedding service
    const user = await prisma.user.findUnique({
      where: { id: auth.id },
    });

    if (!user) {
      throw new Error("User not found");
    }

    const embeddingService = new EmbeddingService(user);

    // Perform semantic search
    const results = await embeddingService.searchSimilar({
      userId: auth.id,
      query: searchQuery,
      categoryId,
      limit,
    });

    return Response.json({
      results,
      query: searchQuery,
      total: results.length,
      limit,
    });
  } catch (error) {
    console.error("Search failed:", error);
    if (error instanceof Error) {
      return new Response(error.message, { status: 500 });
    }
    return new Response("Internal Server Error", { status: 500 });
  }
}