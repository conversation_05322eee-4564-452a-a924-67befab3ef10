import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { BrainCategoryCreate } from "@/lib/brain/types";
import { validateAuth } from "@/lib/middleware/auth";

const categoryCreateSchema = z.object({
  name: z.string().min(1).max(100),
});

const categoryUpdateSchema = z.object({
  name: z.string().min(1).max(100),
});

export async function GET(req: NextRequest) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const categories = await prisma.brainCategory.findMany({
      where: {
        userId: auth.id,
      },
      orderBy: {
        name: "asc",
      },
    });

    return Response.json(categories);
  } catch (error) {
    console.error("Failed to fetch categories:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const auth = await validateAuth(req);
    if (!auth?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const json = await req.json();
    const parsed = categoryCreateSchema.safeParse(json);

    if (!parsed.success) {
      return new Response("Invalid request body", { status: 400 });
    }

    const category: BrainCategoryCreate = {
      userId: auth.id,
      name: parsed.data.name,
    };

    const created = await prisma.brainCategory.create({
      data: category,
    });

    return Response.json(created);
  } catch (error) {
    console.error("Failed to create category:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}