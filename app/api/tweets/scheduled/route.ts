import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const listQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).default('10'),
  status: z.enum(['PENDING', 'PUBLISHED', 'FAILED']).optional(),
  twitterAccountId: z.string().optional()
}).optional();

export async function GET(req: NextRequest) {
  try {
    const user = await authenticateUser(req);

    // Parse query parameters
    const searchParams = Object.fromEntries(req.nextUrl.searchParams);
    const parsed = listQuerySchema.safeParse(searchParams);

    if (!parsed.success) {
      return createErrorResponse('Invalid query parameters', 400);
    }

    const { page = 1, limit = 10, status, twitterAccountId } = parsed.data || {};
    const skip = (page - 1) * limit;

    // Build where clause
    const where = {
      userId: user.id,
      ...(status && { status }),
      ...(twitterAccountId && { twitterAccountId })
    };

    // Get scheduled tweets with pagination
    const [scheduledTweets, total] = await Promise.all([
      prisma.scheduledTweet.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          scheduledAt: 'asc'
        },
        include: {
          twitterAccount: {
            select: {
              id: true,
              screenName: true,
              name: true,
              profilePicture: true
            }
          },
          botPersona: {
            select: {
              id: true,
              name: true,
              description: true
            }
          }
        }
      }),
      prisma.scheduledTweet.count({ where })
    ]);

    // Transform mediaUrlsJson into proper arrays
    const transformedTweets = scheduledTweets.map(tweet => ({
      ...tweet,
      mediaUrls: tweet.mediaUrlsJson ? JSON.parse(tweet.mediaUrlsJson as string) : []
    }));

    return createAuthenticatedResponse({
      tweets: transformedTweets,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + limit < total
      }
    });

  } catch (error) {
    console.error('Error listing scheduled tweets:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}