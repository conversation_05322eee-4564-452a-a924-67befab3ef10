import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { twitterPublisher } from '@/lib/tweets/publisher';
import { z } from 'zod';

const publishTweetSchema = z.object({
  content: z.string().min(1).max(280),
  mediaIds: z.array(z.string()).optional(),
  twitterAccountId: z.string()
});

export async function POST(req: NextRequest) {
  try {
    // Authenticate user
    const user = await authenticateUser(req);

    // Parse and validate request data
    const json = await req.json();
    const parsed = publishTweetSchema.safeParse(json);

    if (!parsed.success) {
      return createErrorResponse('Invalid request data', 400);
    }

    const { content, mediaIds, twitterAccountId } = parsed.data;

    // Get Twitter account and verify ownership
    const twitterAccount = await prisma.twitterAccount.findFirst({
      where: {
        id: twitterAccountId,
        userId: user.id
      }
    });

    if (!twitterAccount) {
      return createErrorResponse('Twitter account not found or access denied', 404);
    }

    // Get media files if provided
    const media = mediaIds?.length ? await prisma.uploadedFile.findMany({
      where: {
        id: { in: mediaIds },
        userId: user.id
      }
    }) : undefined;

    // If media IDs were provided but files weren't found, return error
    if (mediaIds?.length && (!media || media.length !== mediaIds.length)) {
      return createErrorResponse('One or more media files not found', 404);
    }

    // Publish tweet
    const result = await twitterPublisher.publish({
      content,
      twitterAccount,
      media: media?.map(file => ({ file, twitterMediaId: undefined }))
    });

    if (!result.success) {
      console.error('Failed to publish tweet:', result.error);
      return createErrorResponse(
        result.error?.message || 'Failed to publish tweet',
        500
      );
    }

    // Return success response
    return createAuthenticatedResponse({
      tweetId: result.tweetId
    });

  } catch (error) {
    console.error('Error publishing tweet:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}