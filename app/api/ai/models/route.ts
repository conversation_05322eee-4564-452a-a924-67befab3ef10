import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { EnhancedAIProviderFactory } from '@/lib/ai/enhanced-providers';

const modelsRequestSchema = z.object({
  provider: z.enum(['OPENAI', 'GOOGLE']),
  apiKey: z.string().min(1, 'API key is required'),
  type: z.enum(['chat', 'embedding']).optional(),
});

/**
 * POST /api/ai/models - Get available models from AI providers
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { provider, apiKey, type } = modelsRequestSchema.parse(body);

    // Get available models from the provider
    const models = await EnhancedAIProviderFactory.getAvailableModels(provider, apiKey);

    // Filter by type if specified
    let filteredModels = models;
    if (type === 'chat') {
      filteredModels = models.filter(model => 
        !model.includes('embedding') && 
        !model.includes('whisper') && 
        !model.includes('tts') &&
        !model.includes('dall-e')
      );
    } else if (type === 'embedding') {
      filteredModels = models.filter(model => 
        model.includes('embedding')
      );
    }

    return NextResponse.json(filteredModels);
  } catch (error) {
    console.error('Models API error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to fetch models',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
