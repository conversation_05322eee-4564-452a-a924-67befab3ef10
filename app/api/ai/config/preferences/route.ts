import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { AIProvider } from "@prisma/client";
import { OPENAI_MODELS, GOOGLE_MODELS } from "@/lib/types/ai";

export async function PUT(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      defaultProvider,
      defaultChatModel,
      defaultEmbeddingModel
    } = body;

    // Validate provider
    if (defaultProvider && !["OPENAI", "GOOGLE"].includes(defaultProvider)) {
      return NextResponse.json(
        { error: "Invalid provider specified" },
        { status: 400 }
      );
    }

    // Get user's current configuration
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true
      }
    });

    // Validate that the user has configured the selected provider
    if (defaultProvider === "OPENAI" && !userData?.openAiApiKey) {
      return NextResponse.json(
        { error: "OpenAI API key not configured" },
        { status: 400 }
      );
    }

    if (defaultProvider === "GOOGLE" && !userData?.googleApiKey) {
      return NextResponse.json(
        { error: "Google AI API key not configured" },
        { status: 400 }
      );
    }

    // Validate models if provided
    if (defaultChatModel) {
      const validChatModels = [...OPENAI_MODELS, ...GOOGLE_MODELS]
        .filter(m => m.type === "chat")
        .map(m => m.id);
      
      if (!validChatModels.includes(defaultChatModel)) {
        return NextResponse.json(
          { error: "Invalid chat model specified" },
          { status: 400 }
        );
      }
    }

    if (defaultEmbeddingModel) {
      const validEmbeddingModels = [...OPENAI_MODELS, ...GOOGLE_MODELS]
        .filter(m => m.type === "embedding")
        .map(m => m.id);
      
      if (!validEmbeddingModels.includes(defaultEmbeddingModel)) {
        return NextResponse.json(
          { error: "Invalid embedding model specified" },
          { status: 400 }
        );
      }
    }

    // Update user preferences
    const updateData: any = {};
    
    if (defaultProvider) {
      updateData.defaultAiProvider = defaultProvider as AIProvider;
    }
    
    if (defaultChatModel) {
      updateData.defaultChatModel = defaultChatModel;
    }
    
    if (defaultEmbeddingModel) {
      updateData.defaultEmbeddingModel = defaultEmbeddingModel;
    }

    await prisma.user.update({
      where: { id: user.id },
      data: updateData
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to update AI preferences:", error);
    return NextResponse.json(
      { error: "Failed to update AI preferences" },
      { status: 500 }
    );
  }
}