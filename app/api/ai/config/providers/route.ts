import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { AIProvider } from "@prisma/client";

export async function GET(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get current user's provider configurations
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        defaultAiProvider: true
      }
    });

    // Get available providers with their status
    const providers = [
      {
        id: "OPENAI" as AIProvider,
        name: "OpenAI",
        description: "GPT-4, GPT-3.5 and advanced embedding models",
        website: "https://openai.com",
        supported: {
          chat: true,
          embeddings: true,
        },
        configured: !!userData?.openAiApiKey,
        isDefault: userData?.defaultAiProvider === "OPENAI"
      },
      {
        id: "GOOGLE" as AIProvider,
        name: "Google AI",
        description: "Gemini models and embedding capabilities",
        website: "https://ai.google.dev",
        supported: {
          chat: true,
          embeddings: true,
        },
        configured: !!userData?.googleApiKey,
        isDefault: userData?.defaultAiProvider === "GOOGLE"
      }
    ];

    return NextResponse.json({ providers });
  } catch (error) {
    console.error("Failed to fetch AI providers:", error);
    return NextResponse.json(
      { error: "Failed to fetch AI providers" },
      { status: 500 }
    );
  }
}