import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { OpenAIProvider } from "@/lib/ai/providers/openai";
import { GoogleAIProvider } from "@/lib/ai/providers/google-genai";
import { AIProvider } from "@prisma/client";
import { AIConfigurationError } from "@/lib/types/ai";

// Simple encryption for API keys (in production, use a more secure encryption method)
function encryptKey(key: string): Buffer {
  return Buffer.from(key, 'utf-8');
}

function decryptKey(encryptedKey: Buffer): string {
  return encryptedKey.toString('utf-8');
}

export async function GET(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's API key status
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        aiProvider: true
      }
    });

    if (!userData) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    const keyStatus = {
      openai: {
        configured: !!userData.openAiApiKey,
        valid: false,
        error: null as string | null
      },
      google: {
        configured: !!userData.googleApiKey,
        valid: false,
        error: null as string | null
      },
      currentProvider: userData.aiProvider
    };

    // Validate OpenAI key if configured
    if (userData.openAiApiKey) {
      try {
        const decryptedKey = decryptKey(userData.openAiApiKey);
        const openai = new OpenAIProvider({
          apiKey: decryptedKey,
          defaultChatModel: "gpt-4-turbo-preview",
          defaultEmbeddingModel: "text-embedding-3-small"
        });
        await openai.validateConfig();
        keyStatus.openai.valid = true;
      } catch (error) {
        keyStatus.openai.error = error instanceof Error ? error.message : "Invalid API key";
      }
    }

    // Validate Google key if configured
    if (userData.googleApiKey) {
      try {
        const decryptedKey = decryptKey(userData.googleApiKey);
        const google = new GoogleAIProvider({
          apiKey: decryptedKey,
          defaultChatModel: "gemini-2.0-flash-exp",
          defaultEmbeddingModel: "text-embedding-004"
        });
        await google.validateConfig();
        keyStatus.google.valid = true;
      } catch (error) {
        keyStatus.google.error = error instanceof Error ? error.message : "Invalid API key";
      }
    }

    return NextResponse.json({ keyStatus });
  } catch (error) {
    console.error("Failed to get API key status:", error);
    return NextResponse.json(
      { error: "Failed to get API key status" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { provider, apiKey, baseURL, organization } = body;

    if (!provider || !apiKey) {
      return NextResponse.json(
        { error: "Provider and API key are required" },
        { status: 400 }
      );
    }

    // Validate the API key with the provider
    try {
      if (provider === "OPENAI") {
        const openai = new OpenAIProvider({
          apiKey,
          baseURL,
          organization,
          defaultChatModel: "gpt-4-turbo-preview",
          defaultEmbeddingModel: "text-embedding-3-small"
        });
        await openai.validateConfig();
      } else if (provider === "GOOGLE") {
        const google = new GoogleAIProvider({
          apiKey,
          defaultChatModel: "gemini-2.0-flash-exp",
          defaultEmbeddingModel: "text-embedding-004"
        });
        await google.validateConfig();
      } else {
        throw new AIConfigurationError("Invalid provider");
      }
    } catch (error) {
      if (error instanceof AIConfigurationError) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      if (error instanceof Error) {
        return NextResponse.json(
          { error: `Invalid API key: ${error.message}` },
          { status: 400 }
        );
      }
      return NextResponse.json(
        { error: "Invalid API key configuration" },
        { status: 400 }
      );
    }

    // Store the encrypted API key
    const encryptedKey = encryptKey(apiKey);
    
    if (provider === "OPENAI") {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          openAiApiKey: encryptedKey,
          aiProvider: "OPENAI" as AIProvider
        }
      });
    } else {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          googleApiKey: encryptedKey,
          aiProvider: "GOOGLE" as AIProvider
        }
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to store API key:", error);
    return NextResponse.json(
      { error: "Failed to store API key" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider');

    if (!provider) {
      return NextResponse.json(
        { error: "Provider parameter is required" },
        { status: 400 }
      );
    }

    // Remove the API key for the specified provider
    if (provider === "OPENAI") {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          openAiApiKey: null
        }
      });
    } else if (provider === "GOOGLE") {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          googleApiKey: null
        }
      });
    } else {
      return NextResponse.json(
        { error: "Invalid provider" },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to remove API key:", error);
    return NextResponse.json(
      { error: "Failed to remove API key" },
      { status: 500 }
    );
  }
}
