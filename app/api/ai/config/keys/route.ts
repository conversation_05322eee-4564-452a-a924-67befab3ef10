import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { OpenAIProvider } from "@/lib/ai/providers/openai";
import { GoogleAIProvider } from "@/lib/ai/providers/google-genai";
import { AIProvider } from "@prisma/client";
import { AIConfigurationError } from "@/lib/types/ai";

// Simple encryption for API keys (in production, use a more secure encryption method)
function encryptKey(key: string): Buffer {
  return Buffer.from(key, 'utf-8');
}

export async function POST(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { provider, apiKey, baseURL, organization } = body;

    if (!provider || !apiKey) {
      return NextResponse.json(
        { error: "Provider and API key are required" },
        { status: 400 }
      );
    }

    // Validate the API key with the provider
    try {
      if (provider === "OPENAI") {
        const openai = new OpenAIProvider({
          apiKey,
          baseURL,
          organization,
          defaultChatModel: "gpt-4-turbo-preview",
          defaultEmbeddingModel: "text-embedding-3-small"
        });
        await openai.validateConfig();
      } else if (provider === "GOOGLE") {
        const google = new GoogleAIProvider({
          apiKey,
          defaultChatModel: "gemini-2.0-flash-exp",
          defaultEmbeddingModel: "text-embedding-004"
        });
        await google.validateConfig();
      } else {
        throw new AIConfigurationError("Invalid provider");
      }
    } catch (error) {
      if (error instanceof AIConfigurationError) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      if (error instanceof Error) {
        return NextResponse.json(
          { error: `Invalid API key: ${error.message}` },
          { status: 400 }
        );
      }
      return NextResponse.json(
        { error: "Invalid API key configuration" },
        { status: 400 }
      );
    }

    // Store the encrypted API key
    const encryptedKey = encryptKey(apiKey);
    
    if (provider === "OPENAI") {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          openAiApiKey: encryptedKey,
          defaultAiProvider: "OPENAI" as AIProvider
        }
      });
    } else {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          googleApiKey: encryptedKey,
          defaultAiProvider: "GOOGLE" as AIProvider
        }
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Failed to store API key:", error);
    return NextResponse.json(
      { error: "Failed to store API key" },
      { status: 500 }
    );
  }
}