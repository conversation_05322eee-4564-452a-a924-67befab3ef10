import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { OPENAI_MODELS, GOOGLE_MODELS, AIModelConfig } from "@/lib/types/ai";

export async function GET(request: NextRequest) {
  try {
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get user's provider configurations
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        defaultChatModel: true,
        defaultEmbeddingModel: true,
        defaultAiProvider: true
      }
    });

    // Initialize models array
    let availableModels: AIModelConfig[] = [];

    // Add OpenAI models if configured
    if (userData?.openAiApiKey) {
      availableModels = [...availableModels, ...OPENAI_MODELS];
    }

    // Add Google models if configured
    if (userData?.googleApiKey) {
      availableModels = [...availableModels, ...GOOGLE_MODELS];
    }

    // Group models by type
    const chatModels = availableModels.filter(model => model.type === "chat");
    const embeddingModels = availableModels.filter(model => model.type === "embedding");

    // Mark default models
    chatModels.forEach(model => {
      model.isDefault = model.id === userData?.defaultChatModel;
    });

    embeddingModels.forEach(model => {
      model.isDefault = model.id === userData?.defaultEmbeddingModel;
    });

    return NextResponse.json({
      models: {
        chat: chatModels,
        embedding: embeddingModels
      },
      defaultProvider: userData?.defaultAiProvider
    });
  } catch (error) {
    console.error("Failed to fetch AI models:", error);
    return NextResponse.json(
      { error: "Failed to fetch AI models" },
      { status: 500 }
    );
  }
}