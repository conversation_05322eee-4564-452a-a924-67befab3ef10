import { NextRequest } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { requireAuth } from "@/lib/middleware/auth";
import { PersonalityParser } from "@/lib/bots/personality-parser";
import { UserJwtPayload } from "@/lib/types/auth";

// Validation schema for bot creation
const createBotSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  aiProvider: z.enum(["OPENAI", "GOOGLE"]),
  chatModel: z.string().optional(),
  embeddingModel: z.string().optional(),
  persona: z.object({
    name: z.string().min(1).max(50),
    description: z.string().max(500).optional(),
    tone: z.string().min(1).max(100),
    writingStyle: z.string().min(1).max(200),
    vocabulary: z.string().min(1).max(200),
    traits: z.array(z.string()).min(1).max(10),
    knowledge: z.array(z.string()).min(1).max(20),
    rules: z.array(z.string()).min(1).max(10),
    contextLength: z.number().int().min(100).max(2000)
  })
});

/**
 * GET /api/bots - List user's bots
 */
export async function GET(req: NextRequest) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    const bots = await prisma.botPersona.findMany({
      where: {
        userId: user.id
      },
      orderBy: {
        updatedAt: "desc"
      }
    });

    return Response.json({ bots });
  } catch (error) {
    console.error("Failed to list bots:", error);
    return Response.json(
      { error: "Failed to list bots" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/bots - Create a new bot
 */
export async function POST(req: NextRequest) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    const json = await req.json();
    const validatedData = createBotSchema.parse(json);

    // Validate personality data
    try {
      PersonalityParser.parsePersona(JSON.stringify(validatedData.persona));
    } catch (error) {
      return Response.json(
        { error: "Invalid personality data" },
        { status: 400 }
      );
    }

    // Create bot
    const bot = await prisma.botPersona.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        aiProvider: validatedData.aiProvider,
        chatModel: validatedData.chatModel,
        embeddingModel: validatedData.embeddingModel,
        personaContent: validatedData.persona,
        user: {
          connect: {
            id: user.id
          }
        }
      }
    });

    return Response.json({ bot }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Failed to create bot:", error);
    return Response.json(
      { error: "Failed to create bot" },
      { status: 500 }
    );
  }
}