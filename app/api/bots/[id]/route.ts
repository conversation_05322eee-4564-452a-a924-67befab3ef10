import { NextRequest } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { requireAuth } from "@/lib/middleware/auth";
import { PersonalityParser } from "@/lib/bots/personality-parser";
import { UserJwtPayload } from "@/lib/types/auth";

// Validation schema for bot updates
const updateBotSchema = z.object({
  name: z.string().min(1).max(50).optional(),
  description: z.string().max(500).optional(),
  aiProvider: z.enum(["OPENAI", "GOOGLE"]).optional(),
  chatModel: z.string().optional(),
  embeddingModel: z.string().optional(),
  persona: z.object({
    name: z.string().min(1).max(50),
    description: z.string().max(500).optional(),
    tone: z.string().min(1).max(100),
    writingStyle: z.string().min(1).max(200),
    vocabulary: z.string().min(1).max(200),
    traits: z.array(z.string()).min(1).max(10),
    knowledge: z.array(z.string()).min(1).max(20),
    rules: z.array(z.string()).min(1).max(10),
    contextLength: z.number().int().min(100).max(2000)
  }).optional()
});

/**
 * GET /api/bots/[id] - Get bot details
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    const bot = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!bot) {
      return Response.json({ error: "Bot not found" }, { status: 404 });
    }

    return Response.json({ bot });
  } catch (error) {
    console.error("Failed to get bot:", error);
    return Response.json(
      { error: "Failed to get bot details" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/bots/[id] - Update bot
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    // Verify bot ownership
    const existingBot = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!existingBot) {
      return Response.json({ error: "Bot not found" }, { status: 404 });
    }

    const json = await req.json();
    const validatedData = updateBotSchema.parse(json);

    // Validate personality data if provided
    if (validatedData.persona) {
      try {
        PersonalityParser.parsePersona(JSON.stringify(validatedData.persona));
      } catch (error) {
        return Response.json(
          { error: "Invalid personality data" },
          { status: 400 }
        );
      }
    }

    // Update bot
    const updatedBot = await prisma.botPersona.update({
      where: {
        id: params.id
      },
      data: {
        name: validatedData.name,
        description: validatedData.description,
        aiProvider: validatedData.aiProvider,
        chatModel: validatedData.chatModel,
        embeddingModel: validatedData.embeddingModel,
        personaContent: validatedData.persona || undefined
      }
    });

    return Response.json({ bot: updatedBot });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Failed to update bot:", error);
    return Response.json(
      { error: "Failed to update bot" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/bots/[id] - Delete bot
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    // Verify bot ownership
    const bot = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!bot) {
      return Response.json({ error: "Bot not found" }, { status: 404 });
    }

    // Delete bot
    await prisma.botPersona.delete({
      where: {
        id: params.id
      }
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error("Failed to delete bot:", error);
    return Response.json(
      { error: "Failed to delete bot" },
      { status: 500 }
    );
  }
}