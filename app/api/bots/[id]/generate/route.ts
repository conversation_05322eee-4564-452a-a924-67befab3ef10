import { NextRequest } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { requireAuth } from "@/lib/middleware/auth";
import { Bot } from "@/lib/bots/bot";
import { UserJwtPayload } from "@/lib/types/auth";

// Validation schema for generation request
const generateTweetSchema = z.object({
  prompt: z.string().max(500).optional(),
  maxTokens: z.number().min(10).max(1000).optional(),
  useRecentTweets: z.boolean().optional(),
  useBrainEntries: z.boolean().optional(),
  numBrainEntries: z.number().min(1).max(10).optional()
});

/**
 * POST /api/bots/[id]/generate - Generate a tweet using the bot
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    // Get bot with full persona details
    const bot = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!bot) {
      return Response.json({ error: "Bot not found" }, { status: 404 });
    }

    // Validate request
    const json = await req.json();
    const validatedData = generateTweetSchema.parse(json);

    // Initialize bot instance
    const botInstance = new Bot({
      id: bot.id,
      name: bot.name,
      description: bot.description || undefined,
      aiProvider: bot.aiProvider,
      chatModel: bot.chatModel || undefined,
      embeddingModel: bot.embeddingModel || undefined,
      persona: bot.personaContent as any // Type is validated by PersonalityParser
    });

    // Initialize bot (sets up AI provider)
    await botInstance.initialize();

    // Build generation context
    const context: any = {
      userPrompt: validatedData.prompt,
      maxTokens: validatedData.maxTokens
    };

    // Add recent tweets if requested
    if (validatedData.useRecentTweets) {
      const recentTweets = await prisma.scheduledTweet.findMany({
        where: {
          userId: user.id,
          status: "PUBLISHED"
        },
        orderBy: {
          createdAt: "desc"
        },
        take: 5,
        select: {
          content: true
        }
      });
      context.recentTweets = recentTweets.map(t => t.content);
    }

    // Add brain entries if requested
    if (validatedData.useBrainEntries) {
      const brainEntries = await prisma.brainEntry.findMany({
        where: {
          userId: user.id
        },
        orderBy: {
          createdAt: "desc"
        },
        take: validatedData.numBrainEntries || 3,
        select: {
          content: true
        }
      });
      context.brainEntries = brainEntries.map(e => e.content);
    }

    // Generate tweet
    const result = await botInstance.generateTweet(context);

    return Response.json({
      content: result.content,
      tokens: result.tokens
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Failed to generate tweet:", error);
    return Response.json(
      { error: "Failed to generate tweet" },
      { status: 500 }
    );
  }
}