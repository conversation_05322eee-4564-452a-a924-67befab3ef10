import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { validateAuth } from "@/lib/middleware/auth";
import { Bo<PERSON> } from "@/lib/bots/bot";
import { AIProviderError } from "@/lib/types/ai";
import {
  EnhancedAIProviderFactory,
  EnhancedChatSession,
  type AIMessage,
  type AIProviderConfig
} from "@/lib/ai/enhanced-providers";

const chatRequestSchema = z.object({
  message: z.string().min(1, "Message is required").max(2000, "Message too long"),
  conversationHistory: z.array(z.object({
    role: z.enum(["user", "assistant"]),
    content: z.string(),
    timestamp: z.string().optional()
  })).optional().default([])
});

/**
 * POST /api/bots/[id]/chat - Send a message to a bot and get AI response
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const { message, conversationHistory } = chatRequestSchema.parse(body);

    // Get bot and verify ownership
    const botPersona = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!botPersona) {
      return NextResponse.json(
        { error: "Bot not found or access denied" },
        { status: 404 }
      );
    }

    // Get user's AI configuration
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        aiProvider: true,
        chatModel: true,
        embeddingModel: true
      }
    });

    if (!userData) {
      return NextResponse.json(
        { error: "User configuration not found" },
        { status: 404 }
      );
    }

    // Check if user has configured the required AI provider
    const requiredProvider = botPersona.aiProvider;
    const hasRequiredKey = (
      (requiredProvider === "OPENAI" && userData.openAiApiKey) ||
      (requiredProvider === "GOOGLE" && userData.googleApiKey)
    );

    if (!hasRequiredKey) {
      return NextResponse.json(
        { 
          error: `${requiredProvider} API key not configured`,
          suggestion: "Please configure your AI provider API key in the integrations page"
        },
        { status: 400 }
      );
    }

    try {
      // Initialize bot with user's configuration
      const botConfig = {
        id: botPersona.id,
        name: botPersona.name,
        description: botPersona.description || undefined,
        aiProvider: botPersona.aiProvider as any,
        chatModel: botPersona.chatModel || undefined,
        embeddingModel: botPersona.embeddingModel || undefined,
        persona: botPersona.personaContent as any,
      };
      const bot = new Bot(botConfig);

      // Prepare conversation context
      const messages = [
        // System message with bot persona
        {
          role: "system" as const,
          content: bot.getSystemPrompt()
        },
        // Add conversation history
        ...conversationHistory.map(msg => ({
          role: msg.role as "user" | "assistant",
          content: msg.content
        })),
        // Add current user message
        {
          role: "user" as const,
          content: message
        }
      ];

      // Check if streaming is requested
      const isStreaming = request.headers.get('accept')?.includes('text/stream');

      if (isStreaming) {
        // Use enhanced provider for streaming
        const apiKeyBuffer = requiredProvider === "OPENAI" ? userData.openAiApiKey! : userData.googleApiKey!;
        const apiKey = new TextDecoder().decode(apiKeyBuffer);
        const config: AIProviderConfig = {
          apiKey,
          model: botPersona.chatModel || userData.chatModel || (requiredProvider === "OPENAI" ? "gpt-4o" : "gemini-2.0-flash-exp"),
          temperature: 0.7,
          maxTokens: 2000,
        };

        const provider = EnhancedAIProviderFactory.createProvider(requiredProvider as 'OPENAI' | 'GOOGLE', config);
        const chatSession = new EnhancedChatSession(provider, bot.getSystemPrompt());

        // Add conversation history
        conversationHistory.forEach(msg => {
          chatSession.addMessage(msg.role as 'user' | 'assistant', msg.content);
        });

        // Create streaming response
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
          async start(controller) {
            try {
              for await (const chunk of chatSession.sendMessageStream(message)) {
                const data = JSON.stringify({
                  content: chunk.content,
                  done: chunk.done,
                  usage: chunk.usage,
                }) + '\n';
                controller.enqueue(encoder.encode(data));

                if (chunk.done) break;
              }
              controller.close();
            } catch (error) {
              console.error('Streaming error:', error);
              controller.error(error);
            }
          }
        });

        return new NextResponse(stream, {
          headers: {
            'Content-Type': 'text/stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
          },
        });
      }

      // Generate response using the bot (non-streaming)
      const response = await bot.generateResponse(messages);

      // Return successful response
      return NextResponse.json({
        success: true,
        response: {
          role: "assistant",
          content: response,
          timestamp: new Date().toISOString()
        },
        botInfo: {
          id: botPersona.id,
          name: botPersona.name,
          provider: botPersona.aiProvider,
          model: botPersona.chatModel || userData.chatModel
        }
      });

    } catch (error) {
      console.error("Bot chat error:", error);
      
      if (error instanceof AIProviderError) {
        return NextResponse.json(
          { 
            error: "AI provider error",
            details: error.message,
            suggestion: "Please check your AI provider configuration"
          },
          { status: 503 }
        );
      }

      return NextResponse.json(
        { 
          error: "Failed to generate response",
          details: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("Chat API error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Invalid request data",
          details: error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/bots/[id]/chat - Get chat configuration and bot info
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get bot and verify ownership
    const botPersona = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!botPersona) {
      return NextResponse.json(
        { error: "Bot not found or access denied" },
        { status: 404 }
      );
    }

    // Get user's AI configuration
    const userData = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        aiProvider: true,
        chatModel: true,
        embeddingModel: true
      }
    });

    if (!userData) {
      return NextResponse.json(
        { error: "User configuration not found" },
        { status: 404 }
      );
    }

    // Check configuration status
    const requiredProvider = botPersona.aiProvider;
    const hasRequiredKey = (
      (requiredProvider === "OPENAI" && userData.openAiApiKey) ||
      (requiredProvider === "GOOGLE" && userData.googleApiKey)
    );

    return NextResponse.json({
      bot: {
        id: botPersona.id,
        name: botPersona.name,
        description: botPersona.description,
        provider: botPersona.aiProvider,
        chatModel: botPersona.chatModel || userData.chatModel,
        persona: botPersona.personaContent
      },
      configuration: {
        isConfigured: hasRequiredKey,
        provider: requiredProvider,
        model: botPersona.chatModel || userData.chatModel
      },
      limits: {
        maxMessageLength: 2000,
        maxHistoryLength: 50
      }
    });

  } catch (error) {
    console.error("Chat info API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
