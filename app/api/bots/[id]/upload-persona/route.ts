import { NextRequest } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { requireAuth } from "@/lib/middleware/auth";
import { PersonalityParser } from "@/lib/bots/personality-parser";
import { PersonaValidationError } from "@/lib/bots/types";
import { UserJwtPayload } from "@/lib/types/auth";

const MAX_FILE_SIZE = 1024 * 1024; // 1MB

/**
 * POST /api/bots/[id]/upload-persona - Upload and validate persona file
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userResponse = await requireAuth(req);
    if (!('id' in userResponse)) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }
    const user = userResponse as UserJwtPayload;

    // Verify bot ownership
    const bot = await prisma.botPersona.findFirst({
      where: {
        id: params.id,
        userId: user.id
      }
    });

    if (!bot) {
      return Response.json({ error: "Bot not found" }, { status: 404 });
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return Response.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (file.type !== "application/json") {
      return Response.json(
        { error: "Invalid file type. Only JSON files are allowed." },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return Response.json(
        { error: "File size exceeds 1MB limit" },
        { status: 400 }
      );
    }

    // Read and parse file content
    const fileContent = await file.text();
    
    try {
      // Validate persona structure and content
      const persona = PersonalityParser.parsePersona(fileContent);

      // Convert PersonaTemplate to a plain object for Prisma
      const personaObject = {
        name: persona.name,
        description: persona.description,
        tone: persona.tone,
        writingStyle: persona.writingStyle,
        vocabulary: persona.vocabulary,
        traits: persona.traits,
        knowledge: persona.knowledge,
        rules: persona.rules,
        contextLength: persona.contextLength
      };

      // Update bot with new persona
      const updatedBot = await prisma.botPersona.update({
        where: {
          id: params.id
        },
        data: {
          personaContent: personaObject,
          personaType: "FILE"
        }
      });

      return Response.json({ 
        success: true,
        bot: updatedBot
      });
    } catch (error) {
      if (error instanceof PersonaValidationError) {
        return Response.json(
          { error: error.message },
          { status: 400 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("Failed to upload persona:", error);
    return Response.json(
      { error: "Failed to process persona file" },
      { status: 500 }
    );
  }
}