import { NextRequest } from 'next/server';
import { authenticateUser, createAuthenticatedResponse, createErrorResponse } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// Twitter media limits
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'video/mp4',
];
const MAX_FILES = 4;

export async function POST(req: NextRequest) {
  try {
    const user = await authenticateUser(req);

    // Verify request is multipart form-data
    const contentType = req.headers.get('content-type');
    if (!contentType?.includes('multipart/form-data')) {
      return createErrorResponse('Request must be multipart/form-data', 400);
    }

    const formData = await req.formData();
    const files = formData.getAll('files');

    // Validate number of files
    if (!files.length) {
      return createErrorResponse('No files provided', 400);
    }
    if (files.length > MAX_FILES) {
      return createErrorResponse(`Maximum ${MAX_FILES} files allowed`, 400);
    }

    // Process each file
    const uploadedFiles = [];
    for (const file of files) {
      if (!(file instanceof File)) {
        return createErrorResponse('Invalid file data', 400);
      }

      // Validate file type
      if (!ALLOWED_TYPES.includes(file.type)) {
        return createErrorResponse(`File type ${file.type} not allowed`, 400);
      }

      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        return createErrorResponse('File size exceeds 20MB limit', 400);
      }

      try {
        // Get file buffer
        const buffer = await file.arrayBuffer();

        // Upload to storage provider (e.g., S3, Azure Blob, etc.)
        // For this example, we'll use a placeholder URL
        // TODO: Implement actual file storage
        const fileURL = `https://storage.example.com/${user.id}/${file.name}`;

        // Create file record
        const uploadedFile = await prisma.uploadedFile.create({
          data: {
            userId: user.id,
            fileName: file.name,
            fileType: file.type,
            fileSize: file.size,
            fileURL
          }
        });

        uploadedFiles.push(uploadedFile);
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        return createErrorResponse('Error processing file upload', 500);
      }
    }

    return createAuthenticatedResponse({
      files: uploadedFiles
    });

  } catch (error) {
    console.error('Error handling file upload:', error);
    if (error instanceof Error && error.message === 'Unauthorized') {
      return createErrorResponse('Unauthorized', 401);
    }
    return createErrorResponse('Internal server error', 500);
  }
}