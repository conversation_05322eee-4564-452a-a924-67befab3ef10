import { NextRequest, NextResponse } from 'next/server';
import passport from '@/lib/passport/config';
import { AuthenticateOptions } from 'passport';

export async function GET(request: NextRequest) {
  return new Promise((resolve) => {
    const options: AuthenticateOptions = {
      scope: ['profile', 'email'],
      session: false,
    };

    passport.authenticate('google', options)(request as any, {
      redirect: (url: string) => {
        resolve(NextResponse.redirect(url));
      },
    } as any);
  });
}