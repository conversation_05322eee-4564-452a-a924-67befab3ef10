import { NextRequest, NextResponse } from 'next/server';
import passport from '@/lib/passport/config';
import { AuthenticateOptions } from 'passport';

export async function GET(request: NextRequest): Promise<NextResponse> {
  return new Promise<NextResponse>((resolve) => {
    const options: AuthenticateOptions = {
      session: false,
    };

    passport.authenticate('twitter', options)(request as any, {
      redirect: (url: string) => {
        resolve(NextResponse.redirect(url));
      },
    } as any);
  });
}