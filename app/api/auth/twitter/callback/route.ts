import { NextRequest, NextResponse } from 'next/server';
import passport from '@/lib/passport/config';
import { generateAuthToken, setAuthCookie } from '@/lib/middleware/auth';
import { config } from '@/lib/config';
import { AuthUser } from '@/lib/types/auth';

export async function GET(request: NextRequest) {
  return new Promise((resolve) => {
    passport.authenticate('twitter', { session: false }, async (err: any, user: AuthUser) => {
      if (err || !user) {
        return resolve(
          NextResponse.redirect(`${config.app.baseUrl}/login?error=Twitter authentication failed`)
        );
      }

      try {
        // Generate JWT token
        const token = generateAuthToken({
          id: user.id,
          email: user.email,
          name: user.name || undefined,
          image: user.profilePicture || undefined,
        });

        // Create response with successful redirect
        const response = NextResponse.redirect(`${config.app.baseUrl}/dashboard/home`);
        
        // Set auth cookie
        setAuthCookie(response, token);
        
        resolve(response);
      } catch (error) {
        console.error('Twitter callback error:', error);
        resolve(
          NextResponse.redirect(`${config.app.baseUrl}/login?error=Authentication failed`)
        );
      }
    })(request as any, {
      redirect: (url: string) => {
        resolve(NextResponse.redirect(url));
      },
    } as any);
  });
}