import { NextRequest, NextResponse } from 'next/server';
import { validateAuth } from '@/lib/middleware/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user details from database
    const userDetails = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        name: true,
        profilePicture: true,
        createdAt: true,
        updatedAt: true,
        aiProvider: true,
        chatModel: true,
        embeddingModel: true,
        twitterAccounts: {
          select: {
            id: true,
            username: true,
            displayName: true,
            profileImageUrl: true,
            isActive: true,
          }
        },
        _count: {
          select: {
            botPersonas: true,
            scheduledTweets: true,
            brainEntries: true,
          }
        }
      }
    });

    if (!userDetails) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Return user profile data
    return NextResponse.json({
      user: {
        id: userDetails.id,
        email: userDetails.email,
        name: userDetails.name,
        profilePicture: userDetails.profilePicture,
        createdAt: userDetails.createdAt,
        updatedAt: userDetails.updatedAt,
        aiProvider: userDetails.aiProvider,
        chatModel: userDetails.chatModel,
        embeddingModel: userDetails.embeddingModel,
        connectedAccounts: {
          twitter: userDetails.twitterAccounts,
        },
        stats: {
          botPersonas: userDetails._count.botPersonas,
          scheduledTweets: userDetails._count.scheduledTweets,
          brainEntries: userDetails._count.brainEntries,
        }
      }
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    // Validate authentication
    const user = await validateAuth(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { name, profilePicture } = body;

    // Validate input
    if (name && (typeof name !== 'string' || name.trim().length < 2)) {
      return NextResponse.json(
        { error: 'Name must be at least 2 characters long' },
        { status: 400 }
      );
    }

    if (profilePicture && typeof profilePicture !== 'string') {
      return NextResponse.json(
        { error: 'Profile picture must be a valid URL' },
        { status: 400 }
      );
    }

    // Update user profile
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        ...(name && { name: name.trim() }),
        ...(profilePicture !== undefined && { profilePicture }),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        name: true,
        profilePicture: true,
        updatedAt: true,
      }
    });

    return NextResponse.json({
      message: 'Profile updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
