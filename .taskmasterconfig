{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1000000, "temperature": 0.2}, "research": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1000000, "temperature": 0.2}, "fallback": {"provider": "openrouter", "modelId": "microsoft/mai-ds-r1:free", "maxTokens": 120000, "temperature": 0.1}}, "global": {"logLevel": "debug", "debug": true, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/"}}