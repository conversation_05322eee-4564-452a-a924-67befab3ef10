"use client";

import { useState } from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Trash2, <PERSON>Square, BrainCircuit } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";

const mockBots = [
  {
    id: "1",
    name: "Professional Marketer",
    description: "Creates formal, professional marketing content with strong calls to action.",
    personality: "Professional, formal, persuasive",
    createdAt: new Date("2025-03-10"),
    tweetsCount: 42,
  },
  {
    id: "2",
    name: "Casual Conversationalist",
    description: "Writes in a friendly, casual tone perfect for engaging with your audience.",
    personality: "Friendly, casual, engaging",
    createdAt: new Date("2025-02-22"),
    tweetsCount: 86,
  },
  {
    id: "3",
    name: "Tech Enthusiast",
    description: "Specializes in tech content with the right amount of jargon and insight.",
    personality: "Knowledgeable, enthusiastic, informative",
    createdAt: new Date("2025-01-15"),
    tweetsCount: 28,
  },
  {
    id: "4",
    name: "Motivational Coach",
    description: "Shares inspirational and motivational content to inspire your audience.",
    personality: "Inspiring, positive, motivational",
    createdAt: new Date("2025-03-05"),
    tweetsCount: 54,
  },
];

export function BotList() {
  const [bots, setBots] = useState(mockBots);
  const [botToDelete, setBotToDelete] = useState<string | null>(null);
  
  const handleDeleteBot = () => {
    if (botToDelete) {
      setBots(bots.filter(bot => bot.id !== botToDelete));
      setBotToDelete(null);
    }
  };

  return (
    <>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {bots.map((bot) => (
          <Card key={bot.id} className="overflow-hidden transform transition-all hover:shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <Avatar className="h-12 w-12 mb-2">
                  <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                  <AvatarFallback>
                    <Sparkles className="h-6 w-6" />
                  </AvatarFallback>
                </Avatar>
                <div className="space-x-1">
                  <Button variant="ghost" size="icon" asChild>
                    <Link href={`/dashboard/bots/edit/${bot.id}`}>
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Link>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => setBotToDelete(bot.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </div>
              <CardTitle>{bot.name}</CardTitle>
              <CardDescription>{bot.description}</CardDescription>
            </CardHeader>
            <CardContent className="pb-4">
              <div className="flex flex-wrap gap-2 mb-4">
                {bot.personality.split(", ").map((trait) => (
                  <Badge key={trait} variant="secondary">
                    {trait}
                  </Badge>
                ))}
              </div>
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Sparkles className="mr-1 h-4 w-4" />
                  <span>{bot.tweetsCount} tweets</span>
                </div>
                <div>
                  Created {bot.createdAt.toLocaleDateString()}
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t bg-muted/50 px-6 py-3">
              <div className="flex gap-3 w-full">
                <Button variant="outline" className="flex-1" asChild>
                  <Link href={`/dashboard/bots/chat/${bot.id}`}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Chat
                  </Link>
                </Button>
                <Button className="flex-1" asChild>
                  <Link href={`/dashboard/publish/editor?bot=${bot.id}`}>
                    <BrainCircuit className="mr-2 h-4 w-4" />
                    Generate
                  </Link>
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      <AlertDialog open={!!botToDelete} onOpenChange={() => setBotToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this bot persona and all associated data.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteBot} 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}