"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>ci<PERSON>, 
  Trash2, 
  MessageSquare, 
  BrainCircuit, 
  Loader2, 
  AlertCircle,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Eye,
  Copy,
  Download,
  Settings
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";

interface BotPersona {
  id: string;
  name: string;
  description?: string;
  personaContent: {
    name: string;
    description?: string;
    tone: string;
    writingStyle: string;
    vocabulary: string;
    traits: string[];
    knowledge: string[];
    rules: string[];
    contextLength: number;
  };
  personaType: string;
  aiProvider: string;
  chatModel?: string;
  embeddingModel?: string;
  createdAt: string;
  updatedAt: string;
}

type SortOption = 'name' | 'created' | 'updated' | 'provider';
type FilterOption = 'all' | 'OPENAI' | 'GOOGLE';

export function EnhancedBotList() {
  const [bots, setBots] = useState<BotPersona[]>([]);
  const [filteredBots, setFilteredBots] = useState<BotPersona[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [botToDelete, setBotToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('updated');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [selectedBots, setSelectedBots] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchBots();
  }, []);

  useEffect(() => {
    filterAndSortBots();
  }, [bots, searchQuery, sortBy, filterBy]);

  const fetchBots = async () => {
    try {
      const response = await fetch('/api/bots');
      if (!response.ok) {
        throw new Error('Failed to fetch bots');
      }
      const data = await response.json();
      setBots(data.bots);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load bot personas',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterAndSortBots = () => {
    let filtered = bots.filter(bot => {
      const matchesSearch = bot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           bot.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           bot.personaContent.traits.some(trait => 
                             trait.toLowerCase().includes(searchQuery.toLowerCase())
                           );
      const matchesFilter = filterBy === 'all' || bot.aiProvider === filterBy;
      return matchesSearch && matchesFilter;
    });

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'updated':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'provider':
          return a.aiProvider.localeCompare(b.aiProvider);
        default:
          return 0;
      }
    });

    setFilteredBots(filtered);
  };

  const handleDeleteBot = async () => {
    if (!botToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/bots/${botToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete bot');
      }

      setBots(bots.filter(bot => bot.id !== botToDelete));
      setBotToDelete(null);
      
      toast({
        title: 'Success',
        description: 'Bot persona deleted successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete bot persona',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const duplicateBot = async (botId: string) => {
    const bot = bots.find(b => b.id === botId);
    if (!bot) return;

    try {
      const response = await fetch('/api/bots', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: `${bot.name} (Copy)`,
          description: bot.description,
          aiProvider: bot.aiProvider,
          chatModel: bot.chatModel,
          embeddingModel: bot.embeddingModel,
          persona: bot.personaContent,
        }),
      });

      if (!response.ok) throw new Error('Failed to duplicate bot');

      toast({
        title: 'Success',
        description: 'Bot duplicated successfully',
      });

      fetchBots(); // Refresh the list
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to duplicate bot',
        variant: 'destructive',
      });
    }
  };

  const exportBot = (bot: BotPersona) => {
    const exportData = {
      name: bot.name,
      description: bot.description,
      persona: bot.personaContent,
      provider: bot.aiProvider,
      models: {
        chat: bot.chatModel,
        embedding: bot.embeddingModel,
      },
      exported: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${bot.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_bot.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getProviderBadgeColor = (provider: string) => {
    switch (provider) {
      case 'OPENAI':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'GOOGLE':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="flex gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="flex gap-1">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="flex gap-1">
                    <Skeleton className="h-5 w-12" />
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-5 w-14" />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <div className="flex gap-3 w-full">
                  <Skeleton className="h-9 flex-1" />
                  <Skeleton className="h-9 flex-1" />
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (bots.length === 0) {
    return (
      <div className="text-center py-12">
        <Sparkles className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No bot personas yet</h3>
        <p className="mt-2 text-muted-foreground">
          Create your first AI bot persona to get started with automated content generation.
        </p>
        <div className="mt-6">
          <Button asChild>
            <Link href="/dashboard/bots/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Your First Bot
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Header with Actions */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Bot Personas</h2>
            <p className="text-muted-foreground">
              {filteredBots.length} of {bots.length} bots
            </p>
          </div>
          <Button asChild>
            <Link href="/dashboard/bots/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Bot
            </Link>
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search bots by name, description, or traits..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="updated">Recently Updated</SelectItem>
              <SelectItem value="created">Recently Created</SelectItem>
              <SelectItem value="name">Name A-Z</SelectItem>
              <SelectItem value="provider">Provider</SelectItem>
            </SelectContent>
          </Select>

          <Select value={filterBy} onValueChange={(value: FilterOption) => setFilterBy(value)}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Providers</SelectItem>
              <SelectItem value="OPENAI">OpenAI</SelectItem>
              <SelectItem value="GOOGLE">Google AI</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Bot Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredBots.map((bot) => (
            <Card key={bot.id} className="overflow-hidden transform transition-all hover:shadow-lg hover:scale-[1.02]">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <Avatar className="h-12 w-12 mb-2">
                    <AvatarImage src={`https://avatar.vercel.sh/${bot.id}.png`} alt={bot.name} />
                    <AvatarFallback>
                      <Sparkles className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/dashboard/bots/edit/${bot.id}`}>
                          <Pencil className="h-4 w-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => duplicateBot(bot.id)}>
                        <Copy className="h-4 w-4 mr-2" />
                        Duplicate
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => exportBot(bot)}>
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => setBotToDelete(bot.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <CardTitle className="line-clamp-1">{bot.name}</CardTitle>
                <CardDescription className="line-clamp-2">
                  {bot.description || bot.personaContent.description || 'No description provided'}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pb-4">
                <div className="space-y-3">
                  {/* AI Provider Badge */}
                  <div className="flex items-center gap-2">
                    <Badge className={getProviderBadgeColor(bot.aiProvider)}>
                      {bot.aiProvider}
                    </Badge>
                    {bot.chatModel && (
                      <Tooltip>
                        <TooltipTrigger>
                          <Badge variant="outline" className="text-xs">
                            {bot.chatModel.length > 15 ? `${bot.chatModel.substring(0, 15)}...` : bot.chatModel}
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{bot.chatModel}</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>

                  {/* Personality Traits */}
                  <div className="flex flex-wrap gap-1">
                    {bot.personaContent.traits.slice(0, 3).map((trait) => (
                      <Badge key={trait} variant="secondary" className="text-xs">
                        {trait}
                      </Badge>
                    ))}
                    {bot.personaContent.traits.length > 3 && (
                      <Tooltip>
                        <TooltipTrigger>
                          <Badge variant="secondary" className="text-xs">
                            +{bot.personaContent.traits.length - 3} more
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="max-w-xs">
                            <p className="font-medium mb-1">All traits:</p>
                            <p className="text-sm">{bot.personaContent.traits.join(', ')}</p>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <BrainCircuit className="mr-1 h-4 w-4" />
                      <span>{bot.personaContent.tone}</span>
                    </div>
                    <div>
                      {formatDate(bot.createdAt)}
                    </div>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="border-t bg-muted/50 px-6 py-3">
                <div className="flex gap-2 w-full">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" className="flex-1" asChild>
                        <Link href={`/dashboard/bots/chat/${bot.id}`}>
                          <MessageSquare className="mr-2 h-4 w-4" />
                          Chat
                        </Link>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Start a conversation with {bot.name}</p>
                    </TooltipContent>
                  </Tooltip>
                  
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button size="sm" className="flex-1" asChild>
                        <Link href={`/dashboard/publish/editor?bot=${bot.id}`}>
                          <BrainCircuit className="mr-2 h-4 w-4" />
                          Generate
                        </Link>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Generate content with {bot.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredBots.length === 0 && bots.length > 0 && (
          <div className="text-center py-12">
            <Search className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">No bots found</h3>
            <p className="mt-2 text-muted-foreground">
              Try adjusting your search or filter criteria.
            </p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => {
                setSearchQuery('');
                setFilterBy('all');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={!!botToDelete} onOpenChange={() => setBotToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This will permanently delete this bot persona and all associated data.
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleDeleteBot} 
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </TooltipProvider>
  );
}
