"use client";

import { useState } from "react";
import { Calendar as CalendarIcon, Clock } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface ScheduleSelectorProps {
  value?: Date;
  onChange: (date: Date) => void;
}

export function ScheduleSelector({ value, onChange }: ScheduleSelectorProps) {
  const [date, setDate] = useState<Date | undefined>(value);
  const [hour, setHour] = useState<string>(value ? format(value, 'HH') : "12");
  const [minute, setMinute] = useState<string>(value ? format(value, 'mm') : "00");
  const [period, setPeriod] = useState<"AM" | "PM">(
    value ? (parseInt(format(value, 'HH')) >= 12 ? "PM" : "AM") : "AM"
  );

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (!selectedDate) return;
    
    setDate(selectedDate);
    updateDateTime(selectedDate, hour, minute, period);
  };

  const updateDateTime = (
    selectedDate: Date | undefined, 
    hr: string, 
    min: string, 
    pd: "AM" | "PM"
  ) => {
    if (!selectedDate) return;
    
    const newDate = new Date(selectedDate);
    
    // Convert 12-hour format to 24-hour
    let hour24 = parseInt(hr);
    if (pd === "PM" && hour24 < 12) {
      hour24 += 12;
    } else if (pd === "AM" && hour24 === 12) {
      hour24 = 0;
    }
    
    newDate.setHours(hour24, parseInt(min));
    onChange(newDate);
  };

  const hours = Array.from({ length: 12 }, (_, i) => (i + 1).toString().padStart(2, '0'));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  return (
    <div className="flex flex-col space-y-4">
      <div className="grid gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "PPP") : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={date}
              onSelect={handleDateSelect}
              initialFocus
              disabled={(date) => date < new Date()}
            />
          </PopoverContent>
        </Popover>
      </div>
      
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-muted-foreground" />
        <div className="grid grid-cols-3 gap-2">
          <Select
            value={hour}
            onValueChange={(value) => {
              setHour(value);
              updateDateTime(date, value, minute, period);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Hour" />
            </SelectTrigger>
            <SelectContent>
              {hours.map((h) => (
                <SelectItem key={h} value={h}>
                  {h}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={minute}
            onValueChange={(value) => {
              setMinute(value);
              updateDateTime(date, hour, value, period);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Min" />
            </SelectTrigger>
            <SelectContent>
              {["00", "15", "30", "45"].map((m) => (
                <SelectItem key={m} value={m}>
                  {m}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select
            value={period}
            onValueChange={(value: "AM" | "PM") => {
              setPeriod(value);
              updateDateTime(date, hour, minute, value);
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="AM">AM</SelectItem>
              <SelectItem value="PM">PM</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}