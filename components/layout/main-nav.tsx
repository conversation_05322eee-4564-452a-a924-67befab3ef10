"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cva } from "class-variance-authority";
import {
  BookOpen,
  Brain,
  Calendar,
  Edit,
  Home,
  Settings,
  Sparkles,
  Twitter,
} from "lucide-react";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { Separator } from "@/components/ui/separator";

const mainNavItems = [
  {
    title: "Home",
    href: "/dashboard/home",
    icon: <Home className="h-4 w-4 mr-2" />,
  },
  {
    title: "Compose",
    href: "/dashboard/publish/editor",
    icon: <Edit className="h-4 w-4 mr-2" />,
  },
  {
    title: "Schedule",
    href: "/dashboard/schedule/list",
    icon: <Calendar className="h-4 w-4 mr-2" />,
  },
  {
    title: "Knowledge",
    href: "/dashboard/brain/view",
    icon: <Brain className="h-4 w-4 mr-2" />,
  },
  {
    title: "Bots",
    href: "/dashboard/bots/view",
    icon: <Sparkles className="h-4 w-4 mr-2" />,
  },
  {
    title: "Integrations",
    href: "/dashboard/integrations",
    icon: <Settings className="h-4 w-4 mr-2" />,
  },
];

export function MainNav() {
  const pathname = usePathname();

  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList className="gap-1">
        {mainNavItems.map((item) => (
          <NavigationMenuItem key={item.href}>
            <Link href={item.href} legacyBehavior passHref>
              <NavigationMenuLink
                className={cn(
                  navigationMenuTriggerStyle(),
                  "bg-transparent px-4 py-2",
                  pathname === item.href &&
                    "bg-muted font-medium text-foreground",
                )}
              >
                <span className="flex items-center">
                  {item.icon}
                  {item.title}
                </span>
              </NavigationMenuLink>
            </Link>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}

export function MobileNav() {
  const pathname = usePathname();

  return (
    <nav className="md:hidden flex overflow-auto pb-2">
      <div className="flex w-full justify-between px-4 gap-1">
        {mainNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex flex-col items-center justify-center rounded-md px-2 py-2 text-xs font-medium",
              pathname === item.href
                ? "bg-muted text-foreground"
                : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
            )}
          >
            {React.cloneElement(item.icon, { className: "h-5 w-5 mb-1" })}
            {item.title}
          </Link>
        ))}
      </div>
    </nav>
  );
}
