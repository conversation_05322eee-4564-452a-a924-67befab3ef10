"use client";

import { useState } from "react";
import Link from "next/link";
import { Edit, Trash2, <PERSON><PERSON><PERSON>, Tag } from "lucide-react";
import { format } from "date-fns";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { truncateText } from "@/lib/utils";

const mockEntries = [
  {
    id: "1",
    title: "X Best Practices",
    content: "1. Post consistently\n2. Use relevant hashtags\n3. Include visual media\n4. Engage with your audience\n5. Analyze performance metrics",
    category: { id: "marketing", name: "Marketing" },
    createdAt: new Date("2025-03-15T10:30:00"),
    updatedAt: new Date("2025-03-15T14:22:00"),
  },
  {
    id: "2",
    title: "Company Product Launch Announcement",
    content: "We're excited to announce our newest product, featuring advanced AI capabilities and intuitive design. This groundbreaking solution will transform how you manage your workflow.",
    category: { id: "product", name: "Product" },
    createdAt: new Date("2025-03-10T09:15:00"),
    updatedAt: new Date("2025-03-12T11:45:00"),
  },
  {
    id: "3",
    title: "Industry Trends 2025",
    content: "Key trends for 2025 include:\n- AI-powered automation\n- Remote work optimization\n- Blockchain for content verification\n- Voice-first interfaces\n- Privacy-focused technologies",
    category: { id: "industry", name: "Industry News" },
    createdAt: new Date("2025-02-28T14:20:00"),
    updatedAt: new Date("2025-03-05T16:30:00"),
  },
  {
    id: "4",
    title: "Competitor Analysis: TweetBuddy",
    content: "TweetBuddy recently launched their AI writing assistant with the following features:\n- Basic text generation\n- Limited scheduling\n- No knowledge base\n- Pricing: $19/month",
    category: { id: "competitors", name: "Competitors" },
    createdAt: new Date("2025-03-02T11:45:00"),
    updatedAt: new Date("2025-03-02T11:45:00"),
  },
];

export function BrainEntryList() {
  const [entries, setEntries] = useState(mockEntries);
  const [entryToDelete, setEntryToDelete] = useState<string | null>(null);
  
  const handleDeleteEntry = () => {
    if (entryToDelete) {
      setEntries(entries.filter(entry => entry.id !== entryToDelete));
      setEntryToDelete(null);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Knowledge Entries</CardTitle>
          <CardDescription>
            Browse and manage your knowledge base content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Title</TableHead>
                <TableHead className="hidden md:table-cell">Category</TableHead>
                <TableHead className="hidden md:table-cell">Updated</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {entries.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span>{entry.title}</span>
                      <span className="text-xs text-muted-foreground md:hidden">
                        {entry.category.name} • {format(entry.updatedAt, 'MMM d, yyyy')}
                      </span>
                      <p className="mt-1 text-xs text-muted-foreground line-clamp-1 md:hidden">
                        {truncateText(entry.content, 60)}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <Badge variant="outline">{entry.category.name}</Badge>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    {format(entry.updatedAt, 'MMM d, yyyy')}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/brain/view/${entry.id}`}>
                          <BookOpen className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Link>
                      </Button>
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/brain/edit/${entry.id}`}>
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setEntryToDelete(entry.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AlertDialog open={!!entryToDelete} onOpenChange={() => setEntryToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this knowledge base entry.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteEntry} 
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}