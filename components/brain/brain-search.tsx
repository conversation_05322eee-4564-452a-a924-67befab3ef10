"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Spark<PERSON>, Filter } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const mockCategories = [
  { id: "all", name: "All Categories" },
  { id: "marketing", name: "Marketing" },
  { id: "product", name: "Product" },
  { id: "industry", name: "Industry News" },
  { id: "competitors", name: "Competitors" },
];

export function BrainSearch() {
  const router = useRouter();
  const [searchType, setSearchType] = useState<"text" | "ai">("text");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [isSearching, setIsSearching] = useState(false);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);
    
    // Simulate search
    setTimeout(() => {
      setIsSearching(false);
      // Navigate to search results or refresh current page
      // router.push(`/dashboard/brain/view?q=${encodeURIComponent(searchQuery)}&type=${searchType}&category=${selectedCategory}`);
    }, 500);
  };
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>Search Knowledge Base</CardTitle>
        <CardDescription>
          Find entries using text search or AI semantic search
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSearch} className="flex w-full items-center space-x-2">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder={searchType === "text" ? "Search by keywords..." : "Describe what you're looking for..."}
                className="w-full pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button type="button" variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-4">
              <div className="space-y-2">
                <h4 className="font-medium">Filters</h4>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select 
                    value={selectedCategory} 
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          <Select
            value={searchType}
            onValueChange={(value) => setSearchType(value as "text" | "ai")}
          >
            <SelectTrigger className="w-[110px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">
                <div className="flex items-center">
                  <Search className="mr-2 h-4 w-4" />
                  <span>Text</span>
                </div>
              </SelectItem>
              <SelectItem value="ai">
                <div className="flex items-center">
                  <Sparkles className="mr-2 h-4 w-4" />
                  <span>AI Search</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          
          <Button type="submit" disabled={!searchQuery || isSearching}>
            {isSearching ? "Searching..." : "Search"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}