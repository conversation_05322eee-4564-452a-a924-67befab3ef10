// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

datasource db {
  provider   = "postgresql"
  url        = env("DATABASE_URL")
  directUrl  = env("DIRECT_URL")
  extensions = [pgvector(map: "vector")]
}

model User {
  id                    String           @id @default(cuid())
  email                 String           @unique
  hashedPassword        String?
  name                  String?
  googleId             String?          @unique
  twitterId            String?          @unique
  profilePicture       String?
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt
  openAiApiKey         Bytes?          // Encrypted
  googleApiKey         Bytes?          // Encrypted
  defaultAiProvider    String          @default("GOOGLE") // OPENAI or GOOGLE
  defaultChatModel     String          @default("gemini-2.0-flash-exp")
  defaultEmbeddingModel String         @default("text-embedding-004")
  twitterAccounts      TwitterAccount[]
  bot<PERSON><PERSON>as          BotPersona[]
  brainEntries         BrainEntry[]
  scheduledTweets      ScheduledTweet[]
  uploadedFiles        UploadedFile[]
  tokens               Token[]
  categories           BrainCategory[]

  @@index([email])
}

model TwitterAccount {
  id              String           @id @default(cuid())
  userId          String
  twitterUserId   String          @map("twitter_user_id_str")
  screenName      String
  name            String
  profilePicture  String?
  accessToken     Bytes           // Encrypted
  refreshToken    Bytes?          // Encrypted
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@unique([userId, twitterUserId])
  @@index([userId])
}

model BotPersona {
  id              String           @id @default(cuid())
  userId          String
  name            String
  description     String?
  personaContent  Json            // Stores parsed personality file
  personaType     PersonaType     @default(FILE)
  aiProvider      AIProvider      @default(OPENAI)
  chatModel       String?
  embeddingModel  String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  scheduledTweets ScheduledTweet[]

  @@index([userId])
}

model ScheduledTweet {
  id              String         @id @default(cuid())
  userId          String
  content         String         @db.Text
  mediaUrlsJson   Json?         // Array of media URLs
  scheduledAt     DateTime       // Uses timestamptz
  status         TweetStatus    @default(PENDING)
  twitterAccountId String
  botPersonaId    String?
  publishedTweetId String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  twitterAccount  TwitterAccount @relation(fields: [twitterAccountId], references: [id], onDelete: Cascade)
  botPersona      BotPersona?   @relation(fields: [botPersonaId], references: [id])

  @@index([userId])
  @@index([scheduledAt])
  @@index([status])
}

model BrainEntry {
  id          String        @id @default(cuid())
  userId      String
  title       String?
  content     String        @db.Text
  sourceUrl   String?
  embedding   Unsupported("vector(1536)")
  categoryId  String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  category    BrainCategory? @relation(fields: [categoryId], references: [id])

  @@index([userId])
  @@index([categoryId])
}

model BrainCategory {
  id           String       @id @default(cuid())
  userId       String
  name         String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  brainEntries BrainEntry[]

  @@index([userId])
}

model UploadedFile {
  id        String   @id @default(cuid())
  userId    String
  fileName  String
  fileType  String
  fileSize  Int
  fileURL   String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Token {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  type      TokenType
  expiresAt DateTime
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
}

enum TokenType {
  SESSION
  PASSWORD_RESET
}

enum TweetStatus {
  PENDING
  PUBLISHED
  FAILED
}

enum PersonaType {
  FILE
  SCRAPE
}

enum AIProvider {
  OPENAI
  GOOGLE
}