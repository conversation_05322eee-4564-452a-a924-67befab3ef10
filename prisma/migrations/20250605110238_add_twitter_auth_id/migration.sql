-- CreateExtension
CREATE EXTENSION IF NOT EXISTS "vector";

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "TokenType" AS ENUM ('SESSION', 'PASSWORD_RESET');

-- Create<PERSON>num
CREATE TYPE "TweetStatus" AS ENUM ('PENDING', 'PUBLISHED', 'FAILED');

-- CreateEnum
CREATE TYPE "PersonaType" AS ENUM ('FILE', 'SCRAPE');

-- CreateEnum
CREATE TYPE "AIProvider" AS ENUM ('OPENAI', 'GOOGLE');

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "hashedPassword" TEXT,
    "name" TEXT,
    "googleId" TEXT,
    "twitterId" TEXT,
    "profilePicture" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "openAiApiKey" BYTEA,
    "googleApiKey" BYTEA,
    "defaultAiProvider" TEXT NOT NULL DEFAULT 'OPENAI',
    "defaultChatModel" TEXT NOT NULL DEFAULT 'gpt-4-turbo-preview',
    "defaultEmbeddingModel" TEXT NOT NULL DEFAULT 'text-embedding-3-small',

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TwitterAccount" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "twitter_user_id_str" TEXT NOT NULL,
    "screenName" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "profilePicture" TEXT,
    "accessToken" BYTEA NOT NULL,
    "refreshToken" BYTEA,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TwitterAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BotPersona" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "personaContent" JSONB NOT NULL,
    "personaType" "PersonaType" NOT NULL DEFAULT 'FILE',
    "aiProvider" "AIProvider" NOT NULL DEFAULT 'OPENAI',
    "chatModel" TEXT,
    "embeddingModel" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BotPersona_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ScheduledTweet" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "mediaUrlsJson" JSONB,
    "scheduledAt" TIMESTAMP(3) NOT NULL,
    "status" "TweetStatus" NOT NULL DEFAULT 'PENDING',
    "twitterAccountId" TEXT NOT NULL,
    "botPersonaId" TEXT,
    "publishedTweetId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ScheduledTweet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BrainEntry" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT,
    "content" TEXT NOT NULL,
    "sourceUrl" TEXT,
    "embedding" vector(1536) NOT NULL,
    "categoryId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BrainEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BrainCategory" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BrainCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UploadedFile" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "fileURL" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UploadedFile_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Token" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "type" "TokenType" NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Token_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "User_googleId_key" ON "User"("googleId");

-- CreateIndex
CREATE UNIQUE INDEX "User_twitterId_key" ON "User"("twitterId");

-- CreateIndex
CREATE INDEX "User_email_idx" ON "User"("email");

-- CreateIndex
CREATE INDEX "TwitterAccount_userId_idx" ON "TwitterAccount"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "TwitterAccount_userId_twitter_user_id_str_key" ON "TwitterAccount"("userId", "twitter_user_id_str");

-- CreateIndex
CREATE INDEX "BotPersona_userId_idx" ON "BotPersona"("userId");

-- CreateIndex
CREATE INDEX "ScheduledTweet_userId_idx" ON "ScheduledTweet"("userId");

-- CreateIndex
CREATE INDEX "ScheduledTweet_scheduledAt_idx" ON "ScheduledTweet"("scheduledAt");

-- CreateIndex
CREATE INDEX "ScheduledTweet_status_idx" ON "ScheduledTweet"("status");

-- CreateIndex
CREATE INDEX "BrainEntry_userId_idx" ON "BrainEntry"("userId");

-- CreateIndex
CREATE INDEX "BrainEntry_categoryId_idx" ON "BrainEntry"("categoryId");

-- CreateIndex
CREATE INDEX "BrainCategory_userId_idx" ON "BrainCategory"("userId");

-- CreateIndex
CREATE INDEX "UploadedFile_userId_idx" ON "UploadedFile"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "Token_token_key" ON "Token"("token");

-- CreateIndex
CREATE INDEX "Token_userId_idx" ON "Token"("userId");

-- CreateIndex
CREATE INDEX "Token_token_idx" ON "Token"("token");

-- AddForeignKey
ALTER TABLE "TwitterAccount" ADD CONSTRAINT "TwitterAccount_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BotPersona" ADD CONSTRAINT "BotPersona_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduledTweet" ADD CONSTRAINT "ScheduledTweet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduledTweet" ADD CONSTRAINT "ScheduledTweet_twitterAccountId_fkey" FOREIGN KEY ("twitterAccountId") REFERENCES "TwitterAccount"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ScheduledTweet" ADD CONSTRAINT "ScheduledTweet_botPersonaId_fkey" FOREIGN KEY ("botPersonaId") REFERENCES "BotPersona"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BrainEntry" ADD CONSTRAINT "BrainEntry_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BrainEntry" ADD CONSTRAINT "BrainEntry_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "BrainCategory"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BrainCategory" ADD CONSTRAINT "BrainCategory_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UploadedFile" ADD CONSTRAINT "UploadedFile_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Token" ADD CONSTRAINT "Token_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
