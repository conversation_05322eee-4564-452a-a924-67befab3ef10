# Tweetly-NG Technical Architecture Document

[Previous sections 1-11 remain unchanged...]

## 12. Error Handling & Fallback Strategies

### System-Wide Error Handling Architecture

```mermaid
graph TD
    subgraph "Client Layer"
        EH[Error Boundary]
        TH[Toast Handler]
        RR[Request Retry Logic]
    end

    subgraph "Server Layer"
        GEM[Global Error Middleware]
        EL[Error Logger]
        EM[Error Monitor]
    end

    subgraph "Service Layer"
        AIF[AI Fallback Logic]
        DBR[DB Retry Logic]
        TSF[Twitter Service Fallback]
    end

    EH --> TH
    EH --> RR
    GEM --> EL
    GEM --> EM
    AIF --> DBR
    AIF --> TSF
```

### 1. Client-Side Error Handling

#### React Error Boundaries
```typescript
// components/error-boundary.tsx
class GlobalErrorBoundary extends React.Component<Props, State> {
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log to monitoring service
    errorMonitor.captureError(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <FallbackUI error={this.state.error} />;
    }
    return this.props.children;
  }
}
```

#### API Request Error Handling
```typescript
// lib/api-client.ts
class APIClient {
  async request<T>(endpoint: string, config: RequestConfig): Promise<T> {
    const retryConfig = {
      retries: 3,
      backoff: (retryCount: number) => Math.min(1000 * Math.pow(2, retryCount), 10000),
    };

    return retry(async () => {
      try {
        const response = await fetch(endpoint, config);
        
        if (!response.ok) {
          const error = await response.json();
          throw new APIError(error.message, response.status);
        }

        return response.json();
      } catch (error) {
        if (error instanceof APIError) {
          // Handle specific API errors
          if (error.status === 401) {
            await refreshToken();
            // Retry with new token
          }
        }
        throw error;
      }
    }, retryConfig);
  }
}
```

### 2. Server-Side Error Handling

#### Global Error Middleware
```typescript
// middleware/error-handler.ts
export async function errorHandler(
  error: Error,
  request: Request,
  response: Response,
  next: NextFunction
) {
  // Log error details
  logger.error({
    error: error.message,
    stack: error.stack,
    requestId: request.headers['x-request-id'],
    path: request.path
  });

  // Handle specific error types
  if (error instanceof PrismaError) {
    return response.status(500).json({
      error: 'Database operation failed',
      code: 'DB_ERROR'
    });
  }

  if (error instanceof AIServiceError) {
    return response.status(503).json({
      error: 'AI service temporarily unavailable',
      code: 'AI_SERVICE_ERROR'
    });
  }

  if (error instanceof TwitterAPIError) {
    return response.status(502).json({
      error: 'Twitter service error',
      code: 'TWITTER_API_ERROR'
    });
  }

  // Default error response
  return response.status(500).json({
    error: 'An unexpected error occurred',
    code: 'INTERNAL_SERVER_ERROR'
  });
}
```

### 3. Service Layer Error Handling

#### AI Service Fallback Strategy
```typescript
// lib/services/ai.service.ts
class AIService {
  async generateWithFallback(prompt: string): Promise<string> {
    try {
      return await this.primaryProvider.generate(prompt);
    } catch (error) {
      logger.warn('Primary AI provider failed, falling back to secondary', { error });
      
      try {
        return await this.secondaryProvider.generate(prompt);
      } catch (fallbackError) {
        logger.error('Both AI providers failed', { error, fallbackError });
        throw new AIServiceError('All AI providers failed');
      }
    }
  }

  async switchProvider(): Promise<void> {
    const current = this.primaryProvider;
    this.primaryProvider = this.secondaryProvider;
    this.secondaryProvider = current;
    logger.info('Switched AI providers');
  }
}
```

#### Database Retry Logic
```typescript
// lib/services/prisma.service.ts
class PrismaService {
  async withRetry<T>(operation: () => Promise<T>): Promise<T> {
    const maxRetries = 3;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (!this.isRetryableError(error)) {
          throw error;
        }

        await this.delay(Math.pow(2, attempt) * 100);
        
        if (this.isPrismaConnectionError(error)) {
          await prisma.$disconnect();
          await prisma.$connect();
        }
      }
    }

    throw lastError;
  }

  private isRetryableError(error: any): boolean {
    return (
      this.isPrismaConnectionError(error) ||
      this.isDeadlockError(error) ||
      this.isConnectionTimeoutError(error)
    );
  }
}
```

### 4. Error Recovery Strategies

#### Scheduled Tweet Recovery
```typescript
// lib/services/tweet-scheduler.service.ts
class TweetSchedulerService {
  async recoverFailedTweets(): Promise<void> {
    const failedTweets = await prisma.tweet.findMany({
      where: { status: 'FAILED' }
    });

    for (const tweet of failedTweets) {
      try {
        await this.retryTweet(tweet);
      } catch (error) {
        logger.error('Tweet recovery failed', {
          tweetId: tweet.id,
          error
        });
      }
    }
  }

  private async retryTweet(tweet: Tweet): Promise<void> {
    // Implement exponential backoff
    const attempts = tweet.retryCount || 0;
    if (attempts >= 3) {
      await this.notifyUserOfFailure(tweet);
      return;
    }

    await prisma.tweet.update({
      where: { id: tweet.id },
      data: { 
        retryCount: attempts + 1,
        nextRetryAt: new Date(Date.now() + Math.pow(2, attempts) * 1000 * 60)
      }
    });
  }
}
```

#### Brain Entry Vector Regeneration
```typescript
// lib/services/brain.service.ts
class BrainService {
  async regenerateFailedEmbeddings(): Promise<void> {
    const entriesWithoutEmbeddings = await prisma.brainEntry.findMany({
      where: { embedding: null }
    });

    for (const entry of entriesWithoutEmbeddings) {
      try {
        const embedding = await this.aiService.generateEmbedding(entry.content);
        await prisma.brainEntry.update({
          where: { id: entry.id },
          data: { embedding }
        });
      } catch (error) {
        logger.error('Failed to regenerate embedding', {
          entryId: entry.id,
          error
        });
      }
    }
  }
}
```

### 5. Monitoring & Alerting

```typescript
// lib/monitoring/alert-manager.ts
class AlertManager {
  async handleSystemAlert(error: Error, context: AlertContext): Promise<void> {
    const severity = this.calculateSeverity(error, context);
    
    const alert = {
      type: error.name,
      message: error.message,
      severity,
      context,
      timestamp: new Date()
    };

    // Log to monitoring service
    await this.monitor.logAlert(alert);

    // Send notifications based on severity
    if (severity >= AlertSeverity.HIGH) {
      await this.notifyTeam(alert);
    }

    // Trigger automatic recovery if applicable
    if (this.canAutoRecover(error)) {
      await this.triggerRecovery(error, context);
    }
  }

  private calculateSeverity(error: Error, context: AlertContext): AlertSeverity {
    if (error instanceof AIServiceError) {
      return context.isProduction ? AlertSeverity.HIGH : AlertSeverity.MEDIUM;
    }
    
    if (error instanceof TwitterAPIError) {
      return context.affectedUsers > 100 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM;
    }
    
    return AlertSeverity.LOW;
  }
}
```

This comprehensive error handling strategy ensures:
1. Graceful degradation of services
2. Automatic recovery where possible
3. Clear error communication to users
4. Proper logging and monitoring
5. System resilience through fallback mechanisms
6. Efficient error tracking and debugging

Would you like me to expand on any particular aspect of the error handling architecture?