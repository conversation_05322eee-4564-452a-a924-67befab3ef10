# Passport Google OAuth 2.0 & Twitter Auth Documentation

---

## passport-google-oauth20

### Install
```
npm install passport-google-oauth20
```

### Usage
#### Create an Application
- Register an application with Google in the [Google Developers Console](https://console.developers.google.com/).
- Get your client ID and client secret.
- Configure a redirect URI that matches your app's route.

#### Configure Strategy
```js
const GoogleStrategy = require('passport-google-oauth20').Strategy;

passport.use(new GoogleStrategy({
    clientID: GOOGLE_CLIENT_ID,
    clientSecret: GOOGLE_CLIENT_SECRET,
    callbackURL: "http://www.example.com/auth/google/callback"
  },
  function(accessToken, refreshToken, profile, cb) {
    User.findOrCreate({ googleId: profile.id }, function (err, user) {
      return cb(err, user);
    });
  }
));
```

#### Authenticate Requests
```js
app.get('/auth/google',
  passport.authenticate('google', { scope: ['profile'] }));

app.get('/auth/google/callback', 
  passport.authenticate('google', { failureRedirect: '/login' }),
  function(req, res) {
    // Successful authentication, redirect home.
    res.redirect('/');
  });
```

---

## passport-twitter

### Install
```
npm install passport-twitter
```

### Usage
#### Create an Application
- Register your app at [Twitter Application Management](https://apps.twitter.com/).
- Get your consumer key (API Key) and consumer secret (API Secret).
- Configure a callback URL that matches your app's route.

#### Configure Strategy
```js
const TwitterStrategy = require('passport-twitter').Strategy;

passport.use(new TwitterStrategy({
    consumerKey: process.env.TWITTER_CONSUMER_KEY,
    consumerSecret: process.env.TWITTER_CONSUMER_SECRET,
    callbackURL: process.env.TWITTER_CALLBACK_URL
  },
  function(token, tokenSecret, profile, cb) {
    User.findOrCreate({ twitterId: profile.id }, function (err, user) {
      return cb(err, user);
    });
  }
));
```

#### Authenticate Requests
```js
app.get('/auth/twitter',
  passport.authenticate('twitter'));

app.get('/auth/twitter/callback', 
  passport.authenticate('twitter', { failureRedirect: '/login' }),
  function(req, res) {
    // Successful authentication, redirect home.
    res.redirect('/');
  });
```

---

## Notes
- For both Google and Twitter strategies, you must configure your OAuth credentials and callback URLs in the provider's developer console.
- The `verify` callback is where you handle user lookup/creation and call `cb(err, user)` to complete authentication.
- You can adjust scopes in Google OAuth as needed (e.g., `email`, `profile`).
- For advanced usage and more options, refer to the official Passport.js docs and each strategy's README.

