
---

**Tx rat   UI/UX Architecture Document**

**1. Overall Design Philosophy:**

*   **Developer-Focused:** Clean, information-dense, and intuitive interface. Prioritize efficiency and clarity.
*   **Modern & Responsive:** Utilize Tailwind CSS for a modern aesthetic. Ensure responsiveness across desktop and tablet devices.
*   **Consistent Theming:** Support light and dark modes (leveraging `next-themes`).
*   **Action-Oriented:** Clear calls-to-action and feedback mechanisms (notifications, loading states).
*   **Modular Components:** Break down UI into reusable React components.

**2. Global UI Elements & Layouts:**

*   **2.1. Root Layout (`app/layout.tsx`):**
    *   Wraps the entire application.
    *   Includes global context providers (Theme, Auth, Notification, AI, X, Brain, Media).
    *   Sets up global CSS and fonts.
*   **2.2. Authentication Layout (`app/(auth)/layout.tsx`):**
    *   Used for Login, Signup, Password Reset, OTP Verification pages.
    *   Centered content area.
    *   Displays Tweetly logo and name prominently.
    *   Includes a simple footer.
    *   *Reference: `apps/web/app/(auth)/layout.tsx`*
*   **2.3. Main Dashboard Layout (`app/(main)/dashboard/layout.tsx`):**
    *   Used for all authenticated dashboard sections.
    *   **Components:**
        *   **`AppSidebar.tsx`:** Collapsible navigation sidebar.
            *   *Reference: `apps/web/components/app-sidebar.tsx`*
            *   Items: Home, Tweet Brain, Publish, Schedule, Bots, Integrations, Help (at bottom).
        *   **`AppNavbar.tsx`:** Top navigation bar.
            *   *Reference: `apps/web/components/app-navbar.tsx`*
            *   Elements: Sidebar toggle, current section title, Twitter connection status (`TwitterConnected.tsx`), Theme switcher (`ThemeSwitcher.tsx`), User Profile dropdown (`Profile.tsx` with `ProfileMenu.tsx`).
    *   Content area for the specific dashboard page.
*   **2.4. Shared UI Primitives (`components/ui/`):**
    *   `Button.tsx` (Primary, Secondary, Outline variants, with loading states, icons)
    *   `Input.tsx` (Text, Email, Password, with icon support)
    *   `Tooltip.tsx`
    *   `Popup.tsx` (Modal dialog)
    *   `OtpInput.tsx`
    *   `NavbarItem.tsx` (For sidebar and sub-navigation)
    *   *Reference: `apps/web/components/ui/` and `packages/ui/src/`*

**3. Key Pages & UI Flows:**

*   **3.1. Authentication Pages (`app/(auth)/`)**
    *   **3.1.1. Login (`/login`):**
        *   Email and password fields.
        *   "Forgot Password?" link.
        *   Login button (with loading state).
        *   "Or Login With" section for Google/Twitter OAuth.
        *   Link to Signup page.
        *   *Reference: `apps/web/app/(auth)/login/page.tsx`*
    *   **3.1.2. Signup (`/signup`):**
        *   Name, email, password, confirm password fields.
        *   Signup button (with loading state).
        *   "Or Login With" section.
        *   Link to Login page.
        *   *Reference: `apps/web/app/(auth)/signup/page.tsx`*
    *   **3.1.3. OTP Verification (`/signup/verification`):**
        *   Displays email OTP was sent to.
        *   OTP input boxes (`OtpBox.tsx`).
        *   Verify OTP button.
        *   Resend OTP option with timer.
        *   *Reference: `apps/web/app/(auth)/signup/verification/OtpVerification.tsx`*
    *   **3.1.4. Password Reset Request (`/login/passwordreset`):**
        *   Email input field.
        *   Send reset link button.
        *   *Reference: `apps/web/app/(auth)/login/passwordreset/page.tsx`*
    *   **3.1.5. New Password Form (`/login/passwordreset/newpassword`):**
        *   New password and confirm password fields.
        *   Change password button.
        *   *Reference: `apps/web/app/(auth)/login/passwordreset/newpassword/PasswordReset.tsx`*

*   **3.2. Dashboard Home (`app/(main)/dashboard/home/<USER>
    *   Welcome message.
    *   **Components:**
        *   `OnboardingChecklist.tsx`: Guides new users.
        *   `HomeTweetBrain.tsx`: Summary of Tweet Brain stats (tweets, categories).
        *   `HomeBots.tsx`: Summary of active bots.
        *   `PostsCount.tsx` (or similar): Stats on scheduled, drafted, published posts.
    *   Quick actions or links to key features.
    *   *Reference: `apps/web/app/(main)/dashboard/home/<USER>

*   **3.3. Integrations (`app/(main)/dashboard/integrations/page.tsx`)**
    *   Layout: Grid of integration cards.
    *   **Components:**
        *   `TwitterIntegration.tsx`: Connect/disconnect Twitter account, display status and profile info.
        *   `OpenAiIntegration.tsx`: Input for OpenAI API key, validation, status display.
        *   **New:** `GoogleGenAiIntegration.tsx` (similar to OpenAI's): Input for Google AI Studio API key, validation, status.
    *   *Reference: `apps/web/app/(main)/dashboard/integrations/page.tsx` and its components.*

*   **3.4. Bots Section (`app/(main)/dashboard/bots/`)**
    *   **Layout (`layout.tsx`):** Sub-navigation for "Your Bots", "Chat", "Training".
    *   **3.4.1. Your Bots (`/view`):**
        *   Grid display of `BotCard.tsx` components for pre-built and user-defined bots.
        *   Button to "Create Your Bot" / "Upload Persona File".
        *   *Reference: `apps/web/app/(main)/dashboard/bots/view/page.tsx`*
    *   **3.4.2. Chat (`/chat`):**
        *   Two-panel layout:
            *   Left Panel: `BotSelection.tsx` to choose an active bot.
            *   Right Panel: `ChatWithBot.tsx` for message input, chat history display, and quick action prompts (`QuickQuestions.tsx`, `QuickActions.tsx`).
        *   *Reference: `apps/web/app/(main)/dashboard/bots/chat/page.tsx`*
    *   **3.4.3. Training (`/training` - New/Modified):**
        *   Interface for uploading bot personality files (.txt, .md, .json).
        *   Form fields for manual persona input if file upload is not used (or as an alternative).
        *   Display of currently defined personas for editing.
        *   (Optional) Interface for initiating Twitter profile scraping if that feature is kept.

*   **3.5. Tweet Brain Section (`app/(main)/dashboard/brain/`)**
    *   **Layout (`layout.tsx`):** Sub-navigation for "View", "Categories", "AI Search".
    *   **3.5.1. View (`/view`):**
        *   Search input bar.
        *   Category filter pills/buttons (`CategoryItem.tsx`).
        *   `MasonryLayout.tsx` to display saved tweets (using `react-tweet` or custom tweet card).
        *   "Add Content" button triggering `AddContentPopup.tsx`.
        *   Each tweet card in masonry should have options to delete or change category (`CategoryChangePopup.tsx`).
        *   Pagination for many tweets.
        *   *Reference: `apps/web/app/(main)/dashboard/brain/view/page.tsx`*
    *   **3.5.2. Categories (`/categories`):**
        *   List of `CategoryCard.tsx` components.
        *   "Add Category" button triggering `AddCategoryPopup.tsx`.
        *   *Reference: `apps/web/app/(main)/dashboard/brain/categories/page.tsx`*
    *   **3.5.3. AI Search (`/aisearch` - Needs Implementation):**
        *   Large search input for natural language queries.
        *   Results displayed similarly to the "View" page, ranked by semantic similarity.

*   **3.6. Publish Section (`app/(main)/dashboard/publish/`)**
    *   **Layout (`layout.tsx`):** Sub-navigation for "Editor", "Drafts".
    *   **3.6.1. Editor (`/editor`):**
        *   Main content composition area.
        *   **Components:**
            *   `RichTextEditor.tsx`: For tweet text, character count, emoji picker.
            *   `MediaPreview.tsx`: Shows currently attached media.
            *   `PreviewTweet.tsx`: Live preview of the composed tweet.
            *   `SchedulingBar.tsx`: Contains options for "Post Now" / "Schedule", date/time picker (`DateTimePicker.tsx`, `WhenToPost.tsx`), "Save as Draft" button.
            *   Button to trigger `TweetlyIntelligencePopup.tsx` for AI assistance.
            *   Button to trigger `AddMediaPopup.tsx`.
        *   *Reference: `apps/web/app/(main)/dashboard/publish/editor/page.tsx`*
    *   **3.6.2. Drafts (`/drafts`):**
        *   Table or list view of saved draft posts.
        *   Columns: Content snippet, Last Updated, Media count.
        *   Actions per draft: Use Draft (loads into editor), Delete (`DraftPostMenu.tsx`).
        *   Pagination.
        *   *Reference: `apps/web/app/(main)/dashboard/publish/drafts/page.tsx`*

*   **3.7. Schedule Section (`app/(main)/dashboard/schedule/`)**
    *   **Layout (`layout.tsx`):** Sub-navigation for "Scheduled Posts" (List), "Posted".
    *   **3.7.1. Scheduled Posts (`/list`):**
        *   Table or list view of upcoming scheduled posts.
        *   Columns: Content snippet, Scheduled Time, Media count.
        *   Actions per post: Preview (`TweetPreviewPopup.tsx`), Edit (moves to editor, changes status to draft), Delete, Move to Draft (`SchedulePostMenu.tsx`).
        *   Pagination.
        *   *Reference: `apps/web/app/(main)/dashboard/schedule/list/page.tsx`*
    *   **3.7.2. Posted (`/posted`):**
        *   Table or list view of already published posts.
        *   Columns: Content snippet, Published Time, Media count, Link to actual tweet on Twitter/X.
        *   Actions: Preview.
        *   Pagination.
        *   *Reference: `apps/web/app/(main)/dashboard/schedule/posted/page.tsx`*

*   **3.8. Help Section (`app/(main)/dashboard/help/page.tsx`)**
    *   `OnboardingChecklist.tsx`: Interactive checklist for new users.
    *   Embedded tutorial video (e.g., Loom).
    *   Links to documentation or FAQ.
    *   *Reference: `apps/web/app/(main)/dashboard/help/page.tsx`*

**4. Key Reusable Components (from `components/` and `components/ui/`):**

*   **Popups/Modals:**
    *   `AddContentPopup.tsx` (for Tweet Brain)
    *   `AddCategoryPopup.tsx` (for Tweet Brain)
    *   `AddMediaPopup.tsx` (for attaching media to posts)
    *   `AreYouSure.tsx` (confirmation dialog)
    *   `TweetPreviewPopup.tsx`
    *   `PostPopup.tsx` (final review before publish/schedule)
    *   `TweetlyIntelligencePopup.tsx` (AI assistance in composer)
    *   `CategoryChangePopup.tsx`
*   **Cards & Layouts:**
    *   `BotCard.tsx`
    *   `CategoryCard.tsx`
    *   `MasonryLayout.tsx`
*   **Interactive Elements:**
    *   `DateTimePicker.tsx`
    *   `WhenToPost.tsx`
    *   `OtpBox.tsx`
    *   `RichTextEditor.tsx`
    *   `MediaPreview.tsx`
    *   `PreviewTweet.tsx`
    *   `SchedulingBar.tsx`
*   **Menus:**
    *   `DraftPostMenu.tsx`
    *   `ScheduledPostMenu.tsx`
    *   `ProfileMenu.tsx`
*   **Context-Driven UI:**
    *   `TwitterConnected.tsx` (shows status from `AiContext` or `XContext`)
    *   Components displaying data from `BrainContext`, `XContext`, etc.

**5. State Management & Data Flow:**

*   **Context API (`lib/contexts/`):** Centralized state for authentication, AI settings, Twitter data, Brain data, media selections.
*   **Client Components:** Will use these contexts to display data and trigger actions.
*   **API Calls:** Context functions will encapsulate `fetch`/`axios` calls to Next.js API routes.
*   **Server Components:** Used where appropriate for initial data loading to improve performance (e.g., fetching initial list of bots, brain entries).

This UI/UX architecture document provides a clear, structured overview. It connects your existing ideas from `x-webui.txt` with the new requirements and maps them onto a modern Next.js application structure. This should be a very useful guide for development.