

```text
**Project Title:** Tweetly-NG: Unified AI-Powered Twitter/X Management Platform (Next.js Full-Stack)

**Project Overview:**
Build "xtask," a comprehensive, developer-focused web application for AI-assisted Twitter/X management. This platform will enable users to compose, schedule, and publish tweets, manage AI personalities ("bots"), and organize a knowledge base ("Tweet Brain"). The application MUST be a **unified full-stack Next.js application** (using the App Router preferably), where the frontend dashboard and backend API logic reside within a single Next.js project. This approach aims to simplify development, deployment, and CORS management. The platform will integrate with OpenAI and Google Generative AI (using the official `@google/generative-ai` SDK), offering advanced customization for models and embeddings. Data persistence will be handled by a PostgreSQL database, specifically utilizing the `pgvector` extension for semantic search capabilities.

**Core Objective:**
Develop a robust, user-friendly, and performant platform for AI-assisted tweet composition, scheduling, publishing, and knowledge management, all within a single, cohesive Next.js application. The UI/UX should draw heavily from the structure and components detailed in the provided `x-webui.txt` file listing, adapted for the Next.js architecture.

**I. Key Features (Detailed Requirements):**

1.  **User Authentication & Authorization (Next.js API Routes & Frontend):**
    *   **Providers:**
        *   Email/Password (secure password hashing with `bcryptjs`).
        *   Google OAuth 2.0 (using `passport` and `passport-google-oauth20`).
        *   Twitter/X OAuth 1.0a or 2.0 (using `passport` and `passport-twitter`, ensuring it supports posting on behalf of the user).
    *   **Session Management:** Secure session handling (e.g., JWTs stored in httpOnly cookies, managed via Next.js middleware or API route helpers).
    *   **Callback Handling:** OAuth callbacks must be correctly handled within Next.js API routes.
    *   **User Profile:** Basic profile management (display name, email, connected accounts, profile picture). Reference `apps/web/components/profile.tsx` and `apps/web/components/profile-menu.tsx` for UI inspiration.
    *   **Auth UI:** Implement login, signup, password reset, and OTP verification pages as seen in `apps/web/app/(auth)/`.

2.  **AI Provider Integration & Configuration (Next.js API Routes & Frontend Settings):**
    *   **Multi-Provider Support:**
        *   **OpenAI:**
            *   Frontend UI (e.g., in `/dashboard/integrations`) for users to input their OpenAI API Key.
            *   Backend API to validate and store this key securely.
            *   Support for custom `baseURL` (for proxies or self-hosted compatible models).
            *   Support for specifying custom model names for chat/completions (e.g., "gpt-4-turbo-preview", "mistralai/mixtral-8x7b-instruct").
            *   Support for specifying custom embedding model names (e.g., "text-embedding-3-small", "hkunlp/instructor-xl").
        *   **Google Generative AI:**
            *   Integrate using the official **`@google/generative-ai` JavaScript SDK**.
            *   Frontend UI for users to input their Google AI Studio API Key.
            *   Backend API to validate and store this key.
            *   Support for specifying models (e.g., "gemini-pro" for text, compatible embedding models like "text-embedding-004").
    *   **API Key Management:** User-provided API keys (OpenAI, Google GenAI) must be stored securely in the PostgreSQL database, encrypted at rest, and associated with the user account.
    *   **Provider Selection:** Allow users to select their preferred AI provider and specific models for different tasks (e.g., bot generation, Tweet Brain embeddings) via the dashboard settings.
    *   **UI Reference:** The `apps/web/components/open-ai.tsx` and `apps/web/components/twitter-integration.tsx` provide excellent UI starting points for the integrations page (`/dashboard/integrations`).

3.  **AI Bot System (Next.js API Routes & Frontend Dashboard):**
    *   **Bot Persona Definition:**
        *   **Primary Method: Personality File Upload (CRITICAL):**
            *   Allow users to upload a personality definition file (e.g., `.txt`, `.md`, or `.json`) via the UI (e.g., a new section in `/dashboard/bots/training` or similar).
            *   **File Content Structure (JSON preferred, provide schema):**
                ```json
                {
                  "name": "Bot Name",
                  "description": "Short bio for the bot.",
                  "style_guide": {
                    "tone": "witty and informal", // e.g., formal, witty, technical
                    "voice": "first-person", // e.g., first-person, third-person
                    "language_complexity": "intermediate", // e.g., simple, intermediate, advanced
                    "emoji_usage": "moderate", // e.g., none, moderate, frequent
                    "hashtag_usage": "relevant_only", // e.g., none, relevant_only, broad
                    "common_phrases": ["Hey devs!", "Quick tip:"],
                    "length_preference": "short_and_punchy" // e.g., short, medium, detailed
                  },
                  "example_outputs": [
                    "Example tweet 1 showcasing the style.",
                    "Example tweet 2 demonstrating tone.",
                    "Example tweet 3 with typical hashtag usage."
                  ],
                  "core_topics": ["web development", "AI in coding", "Next.js"],
                  "keywords_to_avoid": ["outdated tech", "negative sentiment words"],
                  "target_audience": "Developers, tech enthusiasts, and students."
                }
                ```
            *   The system must parse this file to create/update a bot's persona. Store the parsed content or the file path in the database.
        *   **Optional Secondary Method: Twitter Profile Scraping:** (If implemented, ensure ethical scraping and rate limit respect). Allow users to input a Twitter/X username to infer a persona from recent tweets.
    *   **Bot Management:** CRUD operations for user-created/configured bots. UI similar to `apps/web/app/(main)/dashboard/bots/view/page.tsx` and `apps/web/components/bot-card.tsx`.
    *   **Bot Interaction:** A chat-like interface (`apps/web/components/chat-with-bot.tsx` and `apps/web/components/bot-selection.tsx`) where users can interact with their configured bots. The selected AI provider/model (OpenAI or Google GenAI) will power the responses.
    *   **Contextual Generation:** For bot responses, fetch relevant context (e.g., from `BotTweet` embeddings if implementing tweet-based training, or use the personality file directly) to guide the AI's tone and style.

4.  **Tweet Composer & Publishing (Next.js API Routes & Frontend Dashboard):**
    *   Rich text editor for composing tweets (reference `apps/web/components/rich-text-editor.tsx`).
    *   Character count, emoji picker.
    *   Media Upload: Support for uploading images (and potentially GIFs/videos). Use a service like UploadThing (as seen in `apps/http-server/src/routes/uploadthing.ts` and `apps/web/components/ui/upload-button.tsx`) or Next.js API routes for handling uploads. Store media URLs.
    *   Direct Publishing: Post tweets immediately to connected Twitter/X accounts via Next.js API routes.
    *   Error handling for API failures from Twitter/X.
    *   UI for publishing should be similar to `apps/web/app/(main)/dashboard/publish/editor/page.tsx`.

5.  **Tweet Scheduling (Next.js API Routes & Frontend Dashboard):**
    *   Allow users to schedule tweets for a future date and time (reference `apps/web/components/date-time-picker.tsx` and `apps/web/components/scheduling-bar.tsx`).
    *   A robust backend cron job or task scheduler (e.g., using `node-cron` within a Next.js API route or a separate scheduled function if using serverless deployment) to handle publishing scheduled tweets.
    *   View/edit/delete scheduled tweets (UI similar to `apps/web/app/(main)/dashboard/schedule/list/page.tsx`).

6.  **Tweet Brain (Knowledge Base - Next.js API Routes & Frontend Dashboard):**
    *   **Storage:** Allow users to save their own tweets, interesting tweets from others (e.g., by URL), or text snippets into their "Brain."
    *   **Categorization/Tagging:** Users can create categories (UI: `apps/web/components/add-category.tsx`, `apps/web/components/category-card.tsx`) and/or tags to organize Brain entries.
    *   **Vector Embeddings & Semantic Search (CRITICAL with PostgreSQL + `pgvector`):**
        *   When an entry is added to the Brain, generate a vector embedding for its content using the user's selected AI provider/embedding model (OpenAI or Google GenAI).
        *   Store this embedding in the PostgreSQL database using the `pgvector` extension. The Prisma schema must define a `vector` type field.
        *   Implement a semantic search feature (UI: `apps/web/app/(main)/dashboard/brain/aisearch/page.tsx` - currently "Coming Soon", needs implementation) allowing users to search their Brain using natural language queries. The backend (Next.js API route) should convert the search query to an embedding and perform a similarity search against the stored embeddings in PostgreSQL.
    *   **Display:** Masonry layout (UI: `apps/web/components/masonry-layout.tsx`) or list view for Brain entries.
    *   **UI Reference:** The `/dashboard/brain/` section from `x-webui.txt`.

7.  **Dashboard UI (Next.js App Router):**
    *   Structure the dashboard similar to `apps/web/app/(main)/dashboard/`.
    *   Implement main sections: Home (`/home`), Brain (`/brain`), Publish (`/publish`), Schedule (`/schedule`), Bots (`/bots`), Integrations (`/integrations`), Help (`/help`).
    *   Utilize shared UI components from `packages/ui/` and `apps/web/components/ui/`.
    *   Implement theme switching (light/dark) as seen with `next-themes` and `apps/web/components/ui/theme-switcher.tsx`.
    *   Notifications for actions (reference `apps/web/components/notification/notificationContext.tsx`).

**II. Technology Stack & Architecture (Unified Next.js):**

1.  **Framework:** **Next.js (latest stable version, App Router)** with TypeScript.
    *   Backend API logic will be implemented as Next.js API Routes.
    *   Frontend will be React components within the Next.js `app` directory.
2.  **Database:**
    *   **PostgreSQL (version 14+).** Consider NeonJS or similar serverless PostgreSQL for ease of deployment.
    *   **`pgvector` extension:** Must be enabled and used for vector fields.
3.  **ORM:**
    *   **Prisma:** For type-safe database access, schema management, and migrations. The schema needs to be adapted for PostgreSQL and `pgvector`.
4.  **AI SDKs:**
    *   OpenAI: `openai` npm package.
    *   Google Generative AI: **`@google/generative-ai`** npm package.
5.  **Frontend UI:**
    *   React, TypeScript.
    *   Tailwind CSS (configure as per `apps/web/tailwind.config.ts` and `packages/tailwind-config`).
    *   Headless UI components (e.g., Radix UI, Headless UI by Tailwind Labs, or custom components inspired by `apps/web/components/ui/`).
    *   State Management: Adapt existing context providers (`aiContext`, `xContext`, `brainContext`, `mediaContext`, `authContext` from `apps/web/lib/`) for the Next.js environment, or use Zustand/Jotai if more suitable for the complexity.
    *   Form Handling: React Hook Form.
    *   API Communication: `axios` or native `fetch` within client components or Server Actions.
6.  **Backend Libraries (for Next.js API Routes):**
    *   `passport` (and relevant strategies: `passport-google-oauth20`, `passport-twitter`).
    *   `jsonwebtoken` for JWTs.
    *   `bcryptjs` for password hashing.
    *   `node-cron` (or a serverless-friendly alternative like Vercel Cron Jobs) for scheduling.
    *   `multer` (or Next.js API route's built-in body parsing for `multipart/form-data`) for file uploads.
7.  **Development & Build:**
    *   `pnpm` or `yarn` for package management.
    *   ESLint and Prettier for code quality (configure as per `packages/eslint-config/`).

**III. Data Models (Prisma Schema for PostgreSQL with `pgvector`):**

*   `User`: (id, email, hashedPassword, name, googleId, profilePicture, createdAt, updatedAt, openAiApiKey (encrypted), googleApiKey (encrypted), defaultAiProvider, defaultChatModel, defaultEmbeddingModel)
*   `TwitterAccount`: (id, userId (FK to User), twitterUserId_str, screenName, name, profilePicture, accessToken, refreshToken, createdAt, updatedAt)
*   `BotPersona`: (id, userId (FK to User), name, description, **personaContent (JSONB or TEXT for storing parsed personality file)**, personaType: 'FILE' | 'SCRAPE', aiProvider: 'OPENAI' | 'GOOGLE', chatModel, embeddingModel, createdAt, updatedAt)
*   `ScheduledTweet`: (id, userId (FK to User), content (TEXT), mediaUrlsJson (JSONB), scheduledAt (TIMESTAMPTZ), status: 'PENDING' | 'PUBLISHED' | 'FAILED', twitterAccountId (FK to TwitterAccount), botPersonaId (FK to BotPersona, optional), createdAt, updatedAt, publishedTweetId (TEXT, optional))
*   `BrainEntry`: (id, userId (FK to User), title (TEXT, optional), content (TEXT), sourceUrl (TEXT, optional), **embedding Vector(1536)** (or appropriate dimension for chosen model), categoryId (FK to BrainCategory, optional), createdAt, updatedAt)
    *   *Ensure the `embedding` field is correctly configured for `pgvector`.*
*   `BrainCategory`: (id, userId (FK to User), name (TEXT), createdAt, updatedAt)
*   `UploadedFile`: (id, userId (FK to User), fileName, fileType, fileSize, fileURL, createdAt) - For tweet media.
*   `Token`: (id, userId (FK to User), token (TEXT, unique), type: 'SESSION' | 'PASSWORD_RESET', expiresAt (TIMESTAMPTZ))

*(The AI should refine these schemas, ensure proper relations, indexing, and define the `vector` type for `pgvector` in the Prisma schema, possibly using raw SQL for index creation if Prisma doesn't fully support it yet.)*

**IV. API Design (Next.js API Routes):**

*   `/api/auth/*` (e.g., `/google`, `/twitter`, `/login`, `/register`, `/logout`, `/me`, `/otp-verify`, `/password-reset`)
*   `/api/ai-config/*` (e.g., `/keys` (PUT for setting keys), `/models` (GET available models))
*   `/api/bots/*` (CRUD for `BotPersona`, POST `/api/bots/{id}/upload-persona`)
*   `/api/bots/{botId}/generate` (POST with context/prompt)
*   `/api/tweets/publish` (POST)
*   `/api/tweets/schedule` (POST, GET, PUT `/{id}`, DELETE `/{id}`)
*   `/api/brain/entries` (POST, GET, PUT `/{id}`, DELETE `/{id}`)
*   `/api/brain/search` (GET with `query` parameter for semantic search)
*   `/api/brain/categories` (CRUD)
*   `/api/media/upload` (POST for tweet media, using UploadThing or similar)

**V. User Experience (UX) & UI Structure:**
*   **Reference `x-webui.txt`:** The provided file structure (especially `apps/web/`) is a strong indicator of the desired UI.
    *   **Dashboard Layout:** Main layout with a collapsible sidebar (`apps/web/components/app-sidebar.tsx`) and a top navbar (`apps/web/components/app-navbar.tsx`).
    *   **Pages:** Implement pages under `/dashboard/` for Home, Brain (View, Categories, AISearch), Publish (Editor, Drafts), Schedule (List, Posted), Bots (View, Chat, Training - for personality file upload), Integrations, Help.
    *   **Components:** Reuse or reimplement components found in `apps/web/components/` as needed, ensuring they are Next.js compatible.
*   **Onboarding:** Clear guidance for connecting accounts and setting up API keys (reference `apps/web/components/onboarding-checklist.tsx`).
*   **Error Handling:** Informative notifications.
*   **Loading States:** Visual feedback.
*   **Responsiveness:** Desktop and tablet usability.

**VI. Specific Instructions & Constraints:**

1.  **Unified Next.js Architecture:** This is a strict requirement. No separate frontend/backend projects.
2.  **PostgreSQL & `pgvector`:** Database choice is fixed. Ensure Prisma schema and queries are compatible.
3.  **Google GenAI SDK:** Use `@google/generative-ai`.
4.  **Personality File Focus:** Prioritize the personality file upload method for bot creation.
5.  **Security:** Encrypt API keys in the database. Use httpOnly cookies for session tokens. Implement standard security practices.
6.  **Modularity:** Organize code logically within the Next.js project structure (e.g., `app/`, `components/`, `lib/`, `app/api/`).

**VII. Deliverables:**

1.  **Complete Next.js Application Source Code:** A fully functional application meeting all requirements.
2.  **`README.md`:**
    *   Detailed setup instructions: Environment variables (update `.env.example` for PostgreSQL), PostgreSQL database setup with `pgvector` extension enabled, dependencies installation (`pnpm install`), Prisma migration and generation commands.
    *   Instructions on how to run the application in development (`pnpm dev`) and build for production.
    *   Overview of the project structure.
3.  **Prisma Schema File (`prisma/schema.prisma`)** updated for PostgreSQL and `pgvector`.
4.  **Example `.env` file (`.env.example`)** listing all required environment variables, including a PostgreSQL `DATABASE_URL`.
5.  (Optional but helpful) Postman collection or OpenAPI specification for the Next.js API routes.

This prompt is very detailed and should give the AI agent a clear path. Remember to iterate with the agent if it needs clarification on specific UI elements or backend logic details.
```