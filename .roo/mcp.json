{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "task-master-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"GOOGLE_API_KEY": "AIzaSyDkqZkDBn_AmJnYk5WI_iK5CxlpFrWVi4w", "MISTRAL_API_KEY": "qZb61Qb0tqbdoAMsOA54zEvsz9CMMgXW", "OPENROUTER_API_KEY": "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"}, "disabled": false, "alwaysAllow": ["initialize_project", "models", "parse_prd", "get_tasks", "get_task", "next_task", "complexity_report", "set_task_status", "generate", "add_task", "add_subtask", "update", "update_task", "update_subtask", "remove_subtask", "remove_task", "move_task", "add_dependency", "remove_dependency", "validate_dependencies", "fix_dependencies", "expand_all", "expand_task", "analyze_project_complexity", "clear_subtasks"]}, "node-twitter-api-v2 Docs": {"url": "https://gitmcp.io/PLhery/node-twitter-api-v2", "disabled": false, "autoApprove": [], "alwaysAllow": ["fetch_node_twitter_api_docs", "search_node_twitter_code", "fetch_generic_url_content", "search_node_twitter_docs"]}, "js-genai Docs": {"command": "npx", "args": ["mcp-remote", "https://gitmcp.io/googleapis/js-genai/"]}, "shadcn-ui-server": {"command": "npx", "args": ["-y", "shadcn-ui-mcp-server"], "alwaysAllow": ["list_shadcn_components"]}}}