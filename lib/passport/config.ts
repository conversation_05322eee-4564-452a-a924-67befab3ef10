import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { Strategy as TwitterStrategy } from 'passport-twitter';
import { Profile as TwitterProfile } from 'passport-twitter';
import { Profile as GoogleProfile } from 'passport-google-oauth20';
import bcrypt from 'bcryptjs';
import { prisma } from '../prisma';
import { config } from '../config';


passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await prisma.user.findUnique({ where: { id } });
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Local Strategy
passport.use(new LocalStrategy(
  { usernameField: 'email' },
  async (email, password, done) => {
    try {
      const user = await prisma.user.findUnique({ where: { email } });
      if (!user || !user.hashedPassword) {
        return done(null, false, { message: 'Invalid credentials' });
      }

      const isValid = await bcrypt.compare(password, user.hashedPassword);
      if (!isValid) {
        return done(null, false, { message: 'Invalid credentials' });
      }

      return done(null, user);
    } catch (error) {
      return done(error);
    }
  }
));

// Google Strategy
passport.use(new GoogleStrategy(
  {
    clientID: config.auth.google.clientId,
    clientSecret: config.auth.google.clientSecret,
    callbackURL: config.auth.google.callbackUrl
  },
  async (_accessToken: string, _refreshToken: string, profile: GoogleProfile, done: (error: any, user?: any) => void) => {
    try {
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { googleId: profile.id },
            { email: profile.emails?.[0].value }
          ]
        }
      });

      if (existingUser) {
        // Update Google ID if user exists but doesn't have it set
        if (!existingUser.googleId) {
          await prisma.user.update({
            where: { id: existingUser.id },
            data: { googleId: profile.id }
          });
        }
        return done(null, existingUser);
      }

      // Create new user
      const newUser = await prisma.user.create({
        data: {
          email: profile.emails?.[0].value!,
          name: profile.displayName,
          googleId: profile.id,
          profilePicture: profile.photos?.[0].value
        }
      });

      return done(null, newUser);
    } catch (error) {
      return done(error);
    }
  }
));

// Twitter Strategy
passport.use(new TwitterStrategy(
  {
    consumerKey: config.auth.twitter.clientId,
    consumerSecret: config.auth.twitter.clientSecret,
    callbackURL: config.auth.twitter.callbackUrl,
    includeEmail: true
  },
  async (_token: string, _tokenSecret: string, profile: TwitterProfile, done: (error: any, user?: any) => void) => {
    try {
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { twitterId: profile.id },
            { email: profile.emails?.[0].value }
          ]
        }
      });

      if (existingUser) {
        // Update Twitter ID if user exists but doesn't have it set
        if (!existingUser.twitterId) {
          await prisma.user.update({
            where: { id: existingUser.id },
            data: { twitterId: profile.id }
          });
        }
        return done(null, existingUser);
      }

      // Create new user
      const newUser = await prisma.user.create({
        data: {
          email: profile.emails?.[0].value ?? `${profile.username}@twitter.com`,
          name: profile.displayName,
          twitterId: profile.id,
          profilePicture: profile.photos?.[0].value
        }
      });

      return done(null, newUser);
    } catch (error) {
      return done(error);
    }
  }
));

export default passport;