export const config = {
  auth: {
    jwt: {
      secret: process.env.JWT_SECRET as string,
      expiresIn: '7d',
    },
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
      callbackUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/api/auth/google/callback`,
    },
    twitter: {
      clientId: process.env.x_CLIENT_ID as string,
      clientSecret: process.env.x_CLIENT_SECRET as string,
      callbackUrl: process.env.TWITTER_CALLBACK_URL as string,
    },
    session: {
      secret: process.env.SESSION_SECRET as string,
      cookieName: 'auth_token',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    },
  },
  app: {
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL as string,
  },
};