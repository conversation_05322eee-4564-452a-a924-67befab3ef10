import { <PERSON><PERSON>ate<PERSON><PERSON>, <PERSON>Entry } from "@prisma/client";

export interface BrainEntryCreate {
  userId: string;
  title?: string;
  content: string;
  sourceUrl?: string;
  categoryId?: string;
}

export interface BrainEntryUpdate extends Partial<BrainEntryCreate> {
  id: string;
}

export interface BrainCategoryCreate {
  userId: string;
  name: string;
}

export interface BrainCategoryUpdate extends Partial<BrainCategoryCreate> {
  id: string;
}

export interface SearchParams {
  userId: string;
  query: string;
  categoryId?: string;
  limit?: number;
}

export interface SearchResult {
  entry: BrainEntry;
  category?: BrainCategory;
  similarity: number;
}