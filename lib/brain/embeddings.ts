import { <PERSON><PERSON><PERSON><PERSON>, User, Prisma } from "@prisma/client";
import { OpenAIProvider } from "../ai/providers/openai";
import { GoogleAIProvider } from "../ai/providers/google-genai";
import { AIProviderError } from "../types/ai";
import { prisma } from "../prisma";
import { SearchParams, SearchResult } from "./types";

interface RawSearchResult {
  entry: BrainEntry & {
    embedding: number[];
  };
  category: {
    id: string;
    userId: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
  } | null;
  similarity: number;
}

export class EmbeddingService {
  private user: User;
  private provider: OpenAIProvider | GoogleAIProvider;

  constructor(user: User) {
    this.user = user;
    
    // Initialize AI provider based on user preferences
    if (user.defaultAiProvider === "OPENAI" && user.openAiApiKey) {
      const bytes = new Uint8Array(user.openAiApiKey);
      this.provider = new OpenAIProvider({
        apiKey: new TextDecoder().decode(bytes),
        defaultChatModel: user.defaultChatModel,
        defaultEmbeddingModel: user.defaultEmbeddingModel,
      });
    } else if (user.defaultAiProvider === "GOOGLE" && user.googleApiKey) {
      const bytes = new Uint8Array(user.googleApiKey);
      this.provider = new GoogleAIProvider({
        apiKey: new TextDecoder().decode(bytes),
        defaultChatModel: user.defaultChatModel || "gemini-2.0-flash-exp",
        defaultEmbeddingModel: user.defaultEmbeddingModel || "text-embedding-004",
      });
    } else {
      throw new AIProviderError("No valid AI provider configuration found");
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    try {
      return await this.provider.generateEmbedding(text);
    } catch (error) {
      throw new AIProviderError(
        `Failed to generate embedding: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async storeEntry(entry: BrainEntry): Promise<BrainEntry> {
    const embedding = await this.generateEmbedding(entry.content);

    // Use raw query to handle vector type properly
    const [updated] = await prisma.$queryRawUnsafe<BrainEntry[]>(
      `UPDATE "BrainEntry" 
       SET embedding = $1::vector 
       WHERE id = $2 
       RETURNING *`,
      `[${embedding.join(",")}]`,
      entry.id
    );

    if (!updated) {
      throw new Error(`Failed to update entry ${entry.id}`);
    }

    return updated;
  }

  async searchSimilar(params: SearchParams): Promise<SearchResult[]> {
    const { query, categoryId, limit = 10 } = params;
    const embedding = await this.generateEmbedding(query);

    // Use raw query with proper vector handling
    const results = await prisma.$queryRawUnsafe<RawSearchResult[]>(
      `SELECT 
        e.*,
        c.*,
        1 - (e.embedding <=> $1::vector) as similarity
       FROM "BrainEntry" e
       LEFT JOIN "BrainCategory" c ON e."categoryId" = c.id
       WHERE e."userId" = $2
       ${categoryId ? 'AND e."categoryId" = $3' : ''}
       ORDER BY similarity DESC
       LIMIT $4`,
      `[${embedding.join(",")}]`,
      this.user.id,
      ...(categoryId ? [categoryId] : []),
      limit
    );

    return results.map(result => ({
      entry: {
        id: result.entry.id,
        userId: result.entry.userId,
        title: result.entry.title,
        content: result.entry.content,
        sourceUrl: result.entry.sourceUrl,
        categoryId: result.entry.categoryId,
        embedding: result.entry.embedding,
        createdAt: result.entry.createdAt,
        updatedAt: result.entry.updatedAt,
      },
      category: result.category ? {
        id: result.category.id,
        userId: result.category.userId,
        name: result.category.name,
        createdAt: result.category.createdAt,
        updatedAt: result.category.updatedAt,
      } : undefined,
      similarity: result.similarity,
    }));
  }

  async regenerateEmbeddings(userId: string): Promise<void> {
    const entries = await prisma.brainEntry.findMany({
      where: {
        userId,
        embedding: null,
      },
    });

    for (const entry of entries) {
      await this.storeEntry(entry);
    }
  }
}