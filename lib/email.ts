import { config } from './config';

// Email service interface - can be implemented with different providers
interface EmailService {
  sendPasswordResetEmail(to: string, resetToken: string): Promise<void>;
  sendVerificationEmail(to: string, verificationToken: string): Promise<void>;
}

// Simple email service implementation
// In production, replace with a proper email service like SendGrid, Resend, etc.
class SimpleEmailService implements EmailService {
  async sendPasswordResetEmail(to: string, resetToken: string): Promise<void> {
    const resetUrl = `${config.app.baseUrl}/reset-password?token=${resetToken}`;
    
    // For development, just log the email
    if (process.env.NODE_ENV === 'development') {
      console.log(`
        Password Reset Email for ${to}:
        Reset your password by clicking this link: ${resetUrl}
        This link will expire in 1 hour.
      `);
      return;
    }

    // In production, implement actual email sending
    // Example with fetch to an email service:
    /*
    const response = await fetch('https://api.emailservice.com/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.EMAIL_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to,
        subject: 'Reset Your Password - xtasker',
        html: `
          <h2>Reset Your Password</h2>
          <p>You requested a password reset for your xtasker account.</p>
          <p><a href="${resetUrl}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
          <p>This link will expire in 1 hour.</p>
          <p>If you didn't request this, please ignore this email.</p>
        `,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send email');
    }
    */
  }

  async sendVerificationEmail(to: string, verificationToken: string): Promise<void> {
    const verificationUrl = `${config.app.baseUrl}/verify-email?token=${verificationToken}`;
    
    // For development, just log the email
    if (process.env.NODE_ENV === 'development') {
      console.log(`
        Email Verification for ${to}:
        Verify your email by clicking this link: ${verificationUrl}
        This link will expire in 24 hours.
      `);
      return;
    }

    // In production, implement actual email sending
  }
}

// Export email service instance
const emailService = new SimpleEmailService();

export const sendPasswordResetEmail = emailService.sendPasswordResetEmail.bind(emailService);
export const sendVerificationEmail = emailService.sendVerificationEmail.bind(emailService);

export default emailService;
