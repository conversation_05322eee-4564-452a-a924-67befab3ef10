import { <PERSON><PERSON><PERSON>, SendTweetV2Params, ApiResponseError, ApiRequestError } from 'twitter-api-v2';
import { decrypt } from '../utils/encryption';
import { UploadedFile, TwitterAccount } from '@prisma/client';
import { TweetPublisher, TweetPublishOptions, PublishResult, TweetMedia } from './types';
import { RateLimiter } from 'limiter';

// Rate limits for Twitter API v2
const TWEET_RATE_LIMIT = 50; // tweets per 15 minutes window
const MEDIA_UPLOAD_RATE_LIMIT = 25; // uploads per 15 minutes window

export class TwitterPublisher implements TweetPublisher {
  private tweetLimiter: RateLimiter;
  private mediaLimiter: RateLimiter;

  constructor() {
    // Initialize rate limiters
    this.tweetLimiter = new RateLimiter({
      tokensPerInterval: TWEET_RATE_LIMIT,
      interval: 900000 // 15 minutes in milliseconds
    });

    this.mediaLimiter = new RateLimiter({
      tokensPerInterval: MEDIA_UPLOAD_RATE_LIMIT,
      interval: 900000 // 15 minutes in milliseconds
    });
  }

  private async getTwitterClient(account: TwitterAccount): Promise<TwitterApi> {
    const accessToken = await decrypt(account.accessToken);

    if (!accessToken) {
      throw new Error('Invalid Twitter credentials');
    }

    // Enhanced Twitter client with OAuth2 Bearer token
    const client = new TwitterApi(accessToken);

    // Verify credentials and return appropriate client
    try {
      await client.v2.me();
      return client;
    } catch (error) {
      throw new Error(`Twitter authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async waitForRateLimit(limiter: RateLimiter): Promise<void> {
    const remainingTokens = await limiter.removeTokens(1);
    if (remainingTokens < 0) {
      throw new Error('Rate limit exceeded');
    }
  }

  private formatMediaIds(ids: string[]): [string] | [string, string] | [string, string, string] | [string, string, string, string] {
    const count = Math.min(ids.length, 4);
    switch (count) {
      case 1:
        return [ids[0]];
      case 2:
        return [ids[0], ids[1]];
      case 3:
        return [ids[0], ids[1], ids[2]];
      case 4:
        return [ids[0], ids[1], ids[2], ids[3]];
      default:
        return [ids[0]]; // Fallback to single media
    }
  }

  public async publish(options: TweetPublishOptions): Promise<PublishResult> {
    try {
      await this.waitForRateLimit(this.tweetLimiter);

      const client = await this.getTwitterClient(options.twitterAccount);
      
      let mediaIds: string[] = [];
      if (options.media?.length) {
        // Limit to 4 media items as per Twitter's limit
        const mediaToUpload = options.media.slice(0, 4);
        for (const media of mediaToUpload) {
          if (!media.twitterMediaId) {
            const mediaId = await this.uploadMedia(media.file, options.twitterAccount);
            mediaIds.push(mediaId);
          } else {
            mediaIds.push(media.twitterMediaId);
          }
        }
      }

      // Prepare tweet parameters
      const tweetParams: SendTweetV2Params = {
        text: options.content
      };

      if (mediaIds.length > 0) {
        tweetParams.media = {
          media_ids: this.formatMediaIds(mediaIds)
        };
      }

      // Post tweet
      const tweetData = await client.v2.tweet(tweetParams);

      return {
        success: true,
        tweetId: tweetData.data.id
      };

    } catch (error) {
      console.error('Tweet publish error:', error);

      // Enhanced error handling for Twitter API specific errors
      if (error instanceof ApiResponseError) {
        return {
          success: false,
          error: {
            message: `Twitter API Error: ${error.data?.detail || error.data?.title || error.message}`,
            code: `HTTP_${error.code}`
          }
        };
      }

      if (error instanceof ApiRequestError) {
        return {
          success: false,
          error: {
            message: `Twitter Request Error: ${error.message}`,
            code: 'REQUEST_ERROR'
          }
        };
      }

      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          code: error instanceof Error ? error.name : 'UNKNOWN_ERROR'
        }
      };
    }
  }

  public async uploadMedia(file: UploadedFile, twitterAccount: TwitterAccount): Promise<string> {
    try {
      await this.waitForRateLimit(this.mediaLimiter);

      const client = await this.getTwitterClient(twitterAccount);
      
      const response = await fetch(file.fileURL);
      if (!response.ok) {
        throw new Error('Failed to fetch media file');
      }
      
      const buffer = await response.arrayBuffer();
      
      // Upload media using v1 API
      const mediaId = await client.v1.uploadMedia(Buffer.from(buffer), {
        mimeType: file.fileType
      });

      return mediaId;

    } catch (error) {
      console.error('Media upload error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to upload media');
    }
  }
}

// Export singleton instance
export const twitterPublisher = new TwitterPublisher();