import { ScheduledTweet, TwitterAccount, UploadedFile } from '@prisma/client';

export type TweetStatus = 'PENDING' | 'PUBLISHED' | 'FAILED';

export interface TweetMedia {
  file: UploadedFile;
  twitterMediaId?: string;
}

export interface TweetPublishOptions {
  content: string;
  media?: TweetMedia[];
  twitterAccount: TwitterAccount;
}

export interface ScheduledTweetWithDetails extends ScheduledTweet {
  twitterAccount: TwitterAccount;
  media?: TweetMedia[];
}

export interface PublishResult {
  success: boolean;
  tweetId?: string;
  error?: {
    message: string;
    code?: string;
  };
}

export type TweetScheduleInput = {
  content: string;
  scheduledAt: Date;
  twitterAccountId: string;
  mediaIds?: string[];
  botPersonaId?: string;
};

export type TweetUpdateInput = Partial<Omit<TweetScheduleInput, 'twitterAccountId'>>;

export interface TweetPublisher {
  publish(options: TweetPublishOptions): Promise<PublishResult>;
  uploadMedia(file: UploadedFile, twitterAccount: TwitterAccount): Promise<string>;
}