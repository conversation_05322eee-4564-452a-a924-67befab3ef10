import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-bytes-long';
const ALGORITHM = 'aes-256-cbc';

// Ensure encryption key is 32 bytes
const getKey = (): Buffer => {
  const key = Buffer.from(ENCRYPTION_KEY);
  return key.length >= 32 ? key.subarray(0, 32) : Buffer.concat([key, Buffer.alloc(32 - key.length)]);
};

export function encrypt(text: string): Buffer {
  // Create IV
  const iv = randomBytes(16);
  const key = getKey();

  // @ts-ignore - Node's crypto types are overly strict
  const cipher = createCipheriv(ALGORITHM, key, iv);

  // Encrypt
  const encrypted = Buffer.concat([
    iv,
    cipher.update(Buffer.from(text)),
    cipher.final()
  ]);

  return encrypted;
}

export function decrypt(encrypted: <PERSON>uff<PERSON>): string {
  // Extract IV and encrypted content
  const iv = encrypted.subarray(0, 16);
  const key = getKey();
  const encryptedContent = encrypted.subarray(16);

  // @ts-ignore - Node's crypto types are overly strict
  const decipher = createDecipheriv(ALGORITHM, key, iv);

  // Decrypt
  const decrypted = Buffer.concat([
    decipher.update(encryptedContent),
    decipher.final()
  ]);

  return decrypted.toString('utf8');
}