import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient().$extends({
  query: {
    async $allOperations({ args, query, operation }) {
      try {
        return await query(args);
      } catch (error) {
        console.error('Prisma query error:', error);
        throw error;
      }
    },
  },
});
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
