import { User } from '@prisma/client';

export interface UserJwtPayload {
  id: string;
  email: string;
  name?: string;
  image?: string;
}

export interface AuthUser extends User {
  id: string;
  email: string;
  name: string | null;
  hashedPassword: string | null;
  googleId: string | null;
  twitterId: string | null;
  profilePicture: string | null;
}

export interface GoogleProfile {
  id: string;
  displayName: string;
  emails?: Array<{ value: string }>;
  photos?: Array<{ value: string }>;
}

export interface TwitterProfile {
  id: string;
  username: string;
  displayName: string;
  emails?: Array<{ value: string }>;
  photos?: Array<{ value: string }>;
}