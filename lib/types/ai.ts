import { AIProvider } from "@prisma/client";

export interface AIModelConfig {
  id: string;
  name: string;
  provider: AIProvider;
  type: "chat" | "embedding";
  maxTokens: number;
  supportedFeatures: string[];
  isDefault?: boolean;
}

export interface OpenAIConfig {
  apiKey: string;
  baseURL?: string;
  organization?: string;
  defaultChatModel: string;
  defaultEmbeddingModel: string;
}

export interface GoogleAIConfig {
  apiKey: string;
  defaultChatModel: string;
  defaultEmbeddingModel: string;
}

export interface AIProviderConfig {
  provider: AIProvider;
  openai?: OpenAIConfig;
  google?: GoogleAIConfig;
}

// Available models by provider
export const OPENAI_MODELS: AIModelConfig[] = [
  {
    id: "gpt-4-turbo-preview",
    name: "GPT-4 Turbo",
    provider: "OPENAI",
    type: "chat",
    maxTokens: 128000,
    supportedFeatures: ["chat", "completion"]
  },
  {
    id: "gpt-3.5-turbo",
    name: "GPT-3.5 Turbo",
    provider: "OPENAI",
    type: "chat", 
    maxTokens: 16384,
    supportedFeatures: ["chat", "completion"]
  },
  {
    id: "text-embedding-3-small",
    name: "Text Embedding 3 Small",
    provider: "OPENAI",
    type: "embedding",
    maxTokens: 8191,
    supportedFeatures: ["embedding"]
  },
  {
    id: "text-embedding-3-large",
    name: "Text Embedding 3 Large", 
    provider: "OPENAI",
    type: "embedding",
    maxTokens: 8191,
    supportedFeatures: ["embedding"]
  }
];

export const GOOGLE_MODELS: AIModelConfig[] = [
  {
    id: "gemini-2.0-flash-exp",
    name: "Gemini 2.0 Flash (Experimental)",
    provider: "GOOGLE",
    type: "chat",
    maxTokens: 1048576,
    supportedFeatures: ["chat", "completion", "multimodal", "streaming", "function_calling"],
    isDefault: true
  },
  {
    id: "gemini-1.5-pro",
    name: "Gemini 1.5 Pro",
    provider: "GOOGLE",
    type: "chat",
    maxTokens: 2097152,
    supportedFeatures: ["chat", "completion", "multimodal", "streaming", "function_calling"]
  },
  {
    id: "gemini-1.5-flash",
    name: "Gemini 1.5 Flash",
    provider: "GOOGLE",
    type: "chat",
    maxTokens: 1048576,
    supportedFeatures: ["chat", "completion", "multimodal", "streaming", "function_calling"]
  },
  {
    id: "text-embedding-004",
    name: "Text Embedding 004",
    provider: "GOOGLE",
    type: "embedding",
    maxTokens: 2048,
    supportedFeatures: ["embedding"]
  }
];

// Error types
export class AIConfigurationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AIConfigurationError";
  }
}

export class AIProviderError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AIProviderError";
  }
}