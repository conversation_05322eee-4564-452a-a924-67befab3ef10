import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import jwt, { Secret, SignOptions } from 'jsonwebtoken';
import { config } from '../config';
import { UserJwtPayload } from '../types/auth';

export async function validateAuth(request: NextRequest) {
  try {
    const token = cookies().get(config.auth.session.cookieName)?.value;
    
    if (!token) {
      throw new Error('No token found');
    }

    const decoded = jwt.verify(token, config.auth.jwt.secret as Secret) as UserJwtPayload;
    return decoded;
  } catch (error) {
    return null;
  }
}

export async function requireAuth(request: NextRequest) {
  const user = await validateAuth(request);
  
  if (!user) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  return user;
}

export function setAuthCookie(response: NextResponse, token: string) {
  response.cookies.set({
    name: config.auth.session.cookieName,
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: config.auth.session.maxAge,
    path: '/',
  });
}

export function clearAuthCookie(response: NextResponse) {
  response.cookies.delete(config.auth.session.cookieName);
}

export function generateAuthToken(user: UserJwtPayload): string {
  const signOptions: SignOptions = {
    expiresIn: parseInt(config.auth.jwt.expiresIn) || '7d'
  };
  return jwt.sign(user, config.auth.jwt.secret as Secret, signOptions);
}