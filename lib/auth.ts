import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { verify } from 'jsonwebtoken';

// Temporary dev auth - REMOVE IN PRODUCTION
const DEV_ADMIN = {
  id: 'admin',
  email: process.env.ADMIN_EMAIL || '<EMAIL>',
  name: 'Admin User',
};

const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin12345';

export const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface UserJwtPayload {
  id: string;
  email: string;
  name?: string;
  image?: string;
}

export async function getAuthenticatedUser(request: NextRequest): Promise<UserJwtPayload | null> {
  try {
    // Dev mode admin bypass
    if (process.env.NODE_ENV === 'development') {
      const authHeader = request.headers.get('authorization') || '';
      const [type, credentials] = authHeader.split(' ');
      if (type === 'Basic') {
        const [email, password] = Buffer.from(credentials, 'base64')
          .toString()
          .split(':');
        if (email === DEV_ADMIN.email && password === ADMIN_PASSWORD) {
        return DEV_ADMIN;
        }
      }
    }

    // Get the token from the cookie
    const cookieStore = cookies();
    const token = cookieStore.get('auth_token')?.value;

    if (!token) {
      return null;
    }

    // Verify the token
    const decoded = verify(token, JWT_SECRET) as UserJwtPayload;
    return decoded;
  } catch (error) {
    return null;
  }
}

export async function authenticateUser(request: NextRequest): Promise<UserJwtPayload> {
  const user = await getAuthenticatedUser(request);
  
  if (!user) {
    throw new Error('Unauthorized');
  }
  
  return user;
}

export function createAuthenticatedResponse(
  data: any,
  status = 200,
  headers: HeadersInit = {}
): NextResponse {
  return NextResponse.json(
    { success: true, ...data },
    { status, headers }
  );
}

export function createErrorResponse(
  message: string,
  status = 400,
  headers: HeadersInit = {}
): NextResponse {
  return NextResponse.json(
    { success: false, error: message },
    { status, headers }
  );
}