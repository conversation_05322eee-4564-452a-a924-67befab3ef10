import OpenAI from "openai";
import { APIError } from "openai/error";
import { AIConfigurationError, AIProviderError, OpenAIConfig } from "../../types/ai";

export class OpenAIProvider {
  private client: OpenAI;
  private config: OpenAIConfig;

  constructor(config: OpenAIConfig) {
    this.config = config;
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      organization: config.organization,
    });
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      if (error instanceof APIError) {
        throw new AIConfigurationError(`Failed to validate OpenAI configuration: ${error.message}`);
      }
      throw new AIConfigurationError("Failed to validate OpenAI configuration");
    }
  }

  async getAvailableModels(): Promise<{ chat: string[], embedding: string[] }> {
    try {
      const response = await this.client.models.list();
      const models = response.data;

      // Filter chat models (GPT models)
      const chatModels = models
        .filter(model => 
          model.id.includes('gpt') || 
          model.id.includes('o1') ||
          model.id.includes('chatgpt')
        )
        .map(model => model.id)
        .sort();

      // Filter embedding models
      const embeddingModels = models
        .filter(model => model.id.includes('embedding'))
        .map(model => model.id)
        .sort();

      return {
        chat: chatModels,
        embedding: embeddingModels
      };
    } catch (error) {
      if (error instanceof APIError) {
        throw new AIProviderError(`Failed to fetch OpenAI models: ${error.message}`);
      }
      throw new AIProviderError("Failed to fetch OpenAI models");
    }
  }

  async generateChatCompletion(
    messages: { role: "system" | "user" | "assistant"; content: string }[],
    model?: string
  ) {
    try {
      const completion = await this.client.chat.completions.create({
        messages,
        model: model || this.config.defaultChatModel,
        max_tokens: 1000,
      });

      return completion.choices[0].message.content;
    } catch (error) {
      if (error instanceof APIError) {
        throw new AIProviderError(`OpenAI chat completion failed: ${error.message}`);
      }
      throw new AIProviderError("OpenAI chat completion failed");
    }
  }

  async generateEmbedding(text: string, model?: string) {
    try {
      const response = await this.client.embeddings.create({
        input: text,
        model: model || this.config.defaultEmbeddingModel,
      });

      return response.data[0].embedding;
    } catch (error) {
      if (error instanceof APIError) {
        throw new AIProviderError(`OpenAI embedding generation failed: ${error.message}`);
      }
      throw new AIProviderError("OpenAI embedding generation failed");
    }
  }

  // Rate limiting utilities
  private async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;
          
          if (error instanceof APIError && error.status === 429) {
            const backoffTime = Math.pow(2, attempt) * 1000;
            await this.sleep(backoffTime);
            continue;
          }
        }
        
        throw error;
      }
    }
    
    if (lastError) {
      throw lastError;
    }
    
    throw new AIProviderError("Max retries exceeded");
  }
}
