import { GoogleGenAI } from "@google/genai";
import { AIConfigurationError, AIProviderError, GoogleAIConfig } from "../../types/ai";

export class GoogleAIProvider {
  private client: GoogleGenAI;
  private config: GoogleAIConfig;

  constructor(config: GoogleAIConfig) {
    this.config = config;
    this.client = new GoogleGenAI({ apiKey: config.apiKey });
  }

  async validateConfig(): Promise<boolean> {
    try {
      const response = await this.client.models.countTokens({
        model: this.config.defaultChatModel,
        contents: "Test message"
      });
      return !!response;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIConfigurationError(`Failed to validate Google AI configuration: ${error.message}`);
      }
      throw new AIConfigurationError("Failed to validate Google AI configuration");
    }
  }

  async generateChatCompletion(
    messages: { role: "system" | "user" | "assistant"; content: string }[],
    model?: string
  ) {
    try {
      const modelName = model || this.config.defaultChatModel;

      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === "assistant" ? "model" : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Find system instruction if present
      const systemInstruction = messages.find(msg => msg.role === "system")?.content;

      // Create chat session
      const chat = this.client.chats.create({
        model: modelName,
        config: {
          maxOutputTokens: 1000,
          temperature: 0.7,
        },
        ...(systemInstruction && { systemInstruction }),
        history: contents.filter(c => c.role !== "system")
      });

      const lastMessage = messages[messages.length - 1];
      const result = await chat.sendMessage({ message: lastMessage.content });

      return result.text || "";
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI chat completion failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI chat completion failed");
    }
  }

  async generateEmbedding(text: string, model?: string): Promise<number[]> {
    try {
      const modelName = model || this.config.defaultEmbeddingModel;

      const result = await this.client.models.embedContent({
        model: modelName,
        contents: text
      });

      if (!result.embeddings || result.embeddings.length === 0 || !result.embeddings[0].values) {
        throw new Error("No embedding generated");
      }

      return result.embeddings[0].values;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI embedding generation failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI embedding generation failed");
    }
  }

  // Rate limiting utilities
  private async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;

          // Check for rate limit errors in Google AI response
          if (error.message.includes("quota") || error.message.includes("rate limit")) {
            const backoffTime = Math.pow(2, attempt) * 1000;
            await this.sleep(backoffTime);
            continue;
          }
        }

        throw error;
      }
    }

    if (lastError) {
      throw lastError;
    }

    throw new AIProviderError("Max retries exceeded");
  }

  // Streaming support for chat completions
  async generateChatCompletionStream(
    messages: { role: "system" | "user" | "assistant"; content: string }[],
    model?: string
  ) {
    try {
      const modelName = model || this.config.defaultChatModel;

      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === "assistant" ? "model" : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Find system instruction if present
      const systemInstruction = messages.find(msg => msg.role === "system")?.content;

      // Create chat session
      const chat = this.client.chats.create({
        model: modelName,
        config: {
          maxOutputTokens: 1000,
          temperature: 0.7,
        },
        ...(systemInstruction && { systemInstruction }),
        history: contents.filter(c => c.role !== "system")
      });

      const lastMessage = messages[messages.length - 1];
      const result = await chat.sendMessageStream({ message: lastMessage.content });

      return result;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI streaming failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI streaming failed");
    }
  }
}