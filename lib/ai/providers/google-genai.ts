import { GoogleGenAI } from "@google/genai";
import { AIConfigurationError, AIProviderError, GoogleAIConfig } from "../../types/ai";

// Known Google GenAI models (since there's no direct API to list them)
const GOOGLE_CHAT_MODELS = [
  "gemini-2.0-flash-exp",
  "gemini-1.5-pro",
  "gemini-1.5-flash",
  "gemini-1.0-pro",
  "gemini-pro",
  "gemini-pro-vision"
];

const GOOGLE_EMBEDDING_MODELS = [
  "text-embedding-004",
  "embedding-001"
];

export class GoogleAIProvider {
  private client: GoogleGenAI;
  private config: GoogleAIConfig;

  constructor(config: GoogleAIConfig) {
    this.config = config;
    this.client = new GoogleGenAI({ apiKey: config.apiKey });
  }

  async validateConfig(): Promise<boolean> {
    try {
      const response = await this.client.models.countTokens({
        model: this.config.defaultChatModel,
        contents: "Test message"
      });
      return !!response;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIConfigurationError(`Failed to validate Google AI configuration: ${error.message}`);
      }
      throw new AIConfigurationError("Failed to validate Google AI configuration");
    }
  }

  async getAvailableModels(): Promise<{ chat: string[], embedding: string[] }> {
    try {
      // Since Google GenAI doesn't have a direct API to list models,
      // we'll validate each known model with the current API key
      const validChatModels: string[] = [];
      const validEmbeddingModels: string[] = [];

      // Test chat models
      for (const model of GOOGLE_CHAT_MODELS) {
        try {
          await this.client.models.countTokens({
            model: model,
            contents: "test"
          });
          validChatModels.push(model);
        } catch (error) {
          // Model not accessible with current API key, skip it
          console.debug(`Model ${model} not accessible:`, error);
        }
      }

      // Test embedding models
      for (const model of GOOGLE_EMBEDDING_MODELS) {
        try {
          await this.client.models.embedContent({
            model: model,
            contents: "test"
          });
          validEmbeddingModels.push(model);
        } catch (error) {
          // Model not accessible with current API key, skip it
          console.debug(`Embedding model ${model} not accessible:`, error);
        }
      }

      return {
        chat: validChatModels,
        embedding: validEmbeddingModels
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Failed to fetch Google AI models: ${error.message}`);
      }
      throw new AIProviderError("Failed to fetch Google AI models");
    }
  }

  async generateChatCompletion(
    messages: { role: "system" | "user" | "assistant"; content: string }[],
    model?: string
  ) {
    try {
      const modelName = model || this.config.defaultChatModel;

      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === "assistant" ? "model" : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Find system instruction if present
      const systemInstruction = messages.find(msg => msg.role === "system")?.content;

      // Create chat session
      const chat = this.client.chats.create({
        model: modelName,
        config: {
          maxOutputTokens: 1000,
          temperature: 0.7,
        },
        ...(systemInstruction && { systemInstruction }),
        history: contents.filter(c => c.role !== "system")
      });

      const lastMessage = messages[messages.length - 1];
      const result = await chat.sendMessage({ message: lastMessage.content });

      return result.text || "";
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI chat completion failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI chat completion failed");
    }
  }

  async generateEmbedding(text: string, model?: string): Promise<number[]> {
    try {
      const modelName = model || this.config.defaultEmbeddingModel;

      const result = await this.client.models.embedContent({
        model: modelName,
        contents: text
      });

      if (!result.embeddings || result.embeddings.length === 0 || !result.embeddings[0].values) {
        throw new Error("No embedding generated");
      }

      return result.embeddings[0].values;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI embedding generation failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI embedding generation failed");
    }
  }

  // Rate limiting utilities
  private async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        if (error instanceof Error) {
          lastError = error;

          // Check for rate limit errors in Google AI response
          if (error.message.includes("quota") || error.message.includes("rate limit")) {
            const backoffTime = Math.pow(2, attempt) * 1000;
            await this.sleep(backoffTime);
            continue;
          }
        }

        throw error;
      }
    }

    if (lastError) {
      throw lastError;
    }

    throw new AIProviderError("Max retries exceeded");
  }

  // Streaming support for chat completions
  async generateChatCompletionStream(
    messages: { role: "system" | "user" | "assistant"; content: string }[],
    model?: string
  ) {
    try {
      const modelName = model || this.config.defaultChatModel;

      // Convert messages to Google GenAI format
      const contents = messages.map(msg => ({
        role: msg.role === "assistant" ? "model" : msg.role,
        parts: [{ text: msg.content }]
      }));

      // Find system instruction if present
      const systemInstruction = messages.find(msg => msg.role === "system")?.content;

      // Create chat session
      const chat = this.client.chats.create({
        model: modelName,
        config: {
          maxOutputTokens: 1000,
          temperature: 0.7,
        },
        ...(systemInstruction && { systemInstruction }),
        history: contents.filter(c => c.role !== "system")
      });

      const lastMessage = messages[messages.length - 1];
      const result = await chat.sendMessageStream({ message: lastMessage.content });

      return result;
    } catch (error) {
      if (error instanceof Error) {
        throw new AIProviderError(`Google AI streaming failed: ${error.message}`);
      }
      throw new AIProviderError("Google AI streaming failed");
    }
  }
}
