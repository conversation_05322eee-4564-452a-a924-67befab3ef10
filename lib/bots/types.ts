import { AIProvider } from "@prisma/client";

export interface PersonaTemplate {
  name: string;
  description?: string;
  tone: string;
  writingStyle: string;
  vocabulary: string;
  traits: string[];
  knowledge: string[];
  rules: string[];
  contextLength: number;
}

export interface BotConfig {
  id: string;
  name: string;
  description?: string;
  aiProvider: AIProvider;
  chatModel?: string;
  embeddingModel?: string;
  persona: PersonaTemplate;
}

export interface GenerationContext {
  recentTweets?: string[];
  brainEntries?: string[];
  userPrompt?: string;
  maxTokens?: number;
}

export interface GenerationResult {
  content: string;
  tokens: number;
  error?: string;
}

export class PersonaValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "PersonaValidationError";
  }
}

export class BotGenerationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "BotGenerationError";
  }
}