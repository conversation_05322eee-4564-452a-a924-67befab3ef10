import { AIProvider } from "@prisma/client";
import { OpenAIProvider } from "../ai/providers/openai";
import { GoogleAIProvider } from "../ai/providers/google-genai";
import { PersonalityParser } from "./personality-parser";
import {
  BotConfig,
  GenerationContext,
  GenerationResult,
  BotGenerationError,
} from "./types";
import { prisma } from "../prisma";

export class Bot {
  private config: BotConfig;
  private aiProvider!: OpenAIProvider | GoogleAIProvider;
  private systemPrompt: string;

  constructor(config: BotConfig) {
    this.config = config;
    this.systemPrompt = PersonalityParser.generateSystemPrompt(config.persona);
  }

  /**
   * Initialize the bot with AI provider
   */
  async initialize() {
    const user = await prisma.user.findFirst({
      where: {
        botPersonas: {
          some: {
            id: this.config.id
          }
        }
      },
      select: {
        openAiApiKey: true,
        googleApiKey: true,
        defaultChatModel: true,
        defaultEmbeddingModel: true
      }
    });

    if (!user) {
      throw new BotGenerationError("Failed to find user configuration for bot");
    }

    // Initialize appropriate AI provider
    if (this.config.aiProvider === AIProvider.OPENAI) {
      if (!user.openAiApiKey) {
        throw new BotGenerationError("OpenAI API key not configured");
      }

      const apiKey = new TextDecoder().decode(user.openAiApiKey);
      this.aiProvider = new OpenAIProvider({
        apiKey,
        defaultChatModel: this.config.chatModel || user.defaultChatModel,
        defaultEmbeddingModel: this.config.embeddingModel || user.defaultEmbeddingModel
      });
    } else if (this.config.aiProvider === AIProvider.GOOGLE) {
      if (!user.googleApiKey) {
        throw new BotGenerationError("Google AI API key not configured");
      }

      const apiKey = new TextDecoder().decode(user.googleApiKey);
      this.aiProvider = new GoogleAIProvider({
        apiKey,
        defaultChatModel: this.config.chatModel || user.defaultChatModel || "gemini-2.0-flash-exp",
        defaultEmbeddingModel: this.config.embeddingModel || user.defaultEmbeddingModel || "text-embedding-004"
      });
    } else {
      throw new BotGenerationError(`Unsupported AI provider: ${this.config.aiProvider}`);
    }
  }

  /**
   * Generate a tweet based on context
   */
  async generateTweet(context: GenerationContext): Promise<GenerationResult> {
    try {
      // Validate initialization
      if (!this.aiProvider) {
        throw new BotGenerationError("Bot not initialized");
      }

      // Build prompt with context
      const prompt = this.buildPrompt(context);

      // Generate tweet
      const messages = [
        { role: "system" as const, content: this.systemPrompt },
        { role: "user" as const, content: prompt }
      ];

      const tweet = await this.aiProvider.generateChatCompletion(
        messages,
        this.config.chatModel
      );

      // Validate output
      if (!tweet || tweet.length > 280) {
        throw new BotGenerationError("Generated tweet exceeds character limit");
      }

      return {
        content: tweet,
        tokens: this.estimateTokens(tweet)
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new BotGenerationError(`Tweet generation failed: ${error.message}`);
      }
      throw new BotGenerationError("Tweet generation failed");
    }
  }

  /**
   * Build generation prompt from context
   */
  private buildPrompt(context: GenerationContext): string {
    const parts: string[] = ["Generate a tweet that matches the persona."];

    if (context.userPrompt) {
      parts.push(`Topic/Theme: ${context.userPrompt}`);
    }

    if (context.recentTweets?.length) {
      parts.push("Recent tweets for context:");
      parts.push(...context.recentTweets.map(t => `- ${t}`));
    }

    if (context.brainEntries?.length) {
      parts.push("Additional knowledge context:");
      parts.push(...context.brainEntries.map(e => `- ${e}`));
    }

    if (context.maxTokens) {
      parts.push(`Keep response under ${context.maxTokens} tokens.`);
    }

    return parts.join("\n\n");
  }

  /**
   * Estimate token count for rate limiting
   */
  private estimateTokens(text: string): number {
    // Rough estimation: ~4 chars per token
    return Math.ceil(text.length / 4);
  }
}