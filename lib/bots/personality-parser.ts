import { z } from "zod";
import { PersonaTemplate, PersonaValidationError } from "./types";

const personaSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  tone: z.string().min(1).max(100),
  writingStyle: z.string().min(1).max(200),
  vocabulary: z.string().min(1).max(200),
  traits: z.array(z.string()).min(1).max(10),
  knowledge: z.array(z.string()).min(1).max(20),
  rules: z.array(z.string()).min(1).max(10),
  contextLength: z.number().int().min(100).max(2000)
});

export class PersonalityParser {
  /**
   * Parse and validate a persona file
   * @param content Raw file content as string
   * @returns Validated PersonaTemplate
   * @throws PersonaValidationError
   */
  static parsePersona(content: string): PersonaTemplate {
    try {
      // Parse JSON content
      const rawPersona = JSON.parse(content);
      
      // Validate against schema
      const result = personaSchema.safeParse(rawPersona);
      
      if (!result.success) {
        throw new Error(result.error.message);
      }

      // Normalize data
      const persona = result.data;
      
      return {
        name: persona.name.trim(),
        description: persona.description?.trim(),
        tone: persona.tone.trim().toLowerCase(),
        writingStyle: persona.writingStyle.trim(),
        vocabulary: persona.vocabulary.trim(),
        traits: persona.traits.map(t => t.trim()),
        knowledge: persona.knowledge.map(k => k.trim()),
        rules: persona.rules.map(r => r.trim()),
        contextLength: persona.contextLength
      };
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new PersonaValidationError("Invalid JSON format in persona file");
      }
      if (error instanceof Error) {
        throw new PersonaValidationError(
          `Persona validation failed: ${error.message}`
        );
      }
      throw new PersonaValidationError("Failed to parse persona file");
    }
  }

  /**
   * Generate a system prompt from a persona template
   */
  static generateSystemPrompt(persona: PersonaTemplate): string {
    return `You are a Twitter bot with the following personality:

Name: ${persona.name}
${persona.description ? `Description: ${persona.description}\n` : ""}
Tone: ${persona.tone}
Writing Style: ${persona.writingStyle}
Vocabulary Level: ${persona.vocabulary}

Key Traits:
${persona.traits.map(t => `- ${t}`).join("\n")}

Knowledge Areas:
${persona.knowledge.map(k => `- ${k}`).join("\n")}

Rules to Follow:
${persona.rules.map(r => `- ${r}`).join("\n")}

Generate tweets that reflect this personality while staying within Twitter's character limits.`;
  }
}