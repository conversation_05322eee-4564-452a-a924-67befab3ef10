# Environment variables

# Authentication
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin12345

# Database
# Pooled connection for the application
DATABASE_URL=postgresql://xtask_owner:<EMAIL>/xtask?sslmode=require&connect_timeout=10&pool_timeout=15&connection_limit=20

# Direct connection for Prisma migrations
DIRECT_URL=postgresql://xtask_owner:<EMAIL>/xtask?sslmode=require&connect_timeout=10

# AI Services
OPENAI_API_KEY_SERVER_DEFAULT=
GOOGLE_AI_API_KEY_SERVER_DEFAULT=AIzaSyDkqZkDBn_AmJnYk5WI_iK5CxlpFrWVi4w

# Application
NEXT_PUBLIC_BASE_URL=http://localhost:3000



# Google OAuth Configuration
# Get these from: https://console.developers.google.com/
GOOGLE_CLIENT_ID=173940599657-c7glcih8tm568uqlupc22gs77mqccgep.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Google AI (Gemini) Configuration
# Get your API key from: https://ai.google.dev/
GEMINI_API_KEY="AIzaSyDkqZkDBn_AmJnYk5WI_iK5CxlpFrWVi4w"

# Twitter/X API Configuration
# Get these from: https://developer.twitter.com/
TWITTER_CALLBACK_URL=https://api.tasker.violetmethods.com/api/v1/user/path/auth/twitter/callback
x_CLIENT_ID=cVdGaExMZS13cHlHdmdJVVY0T0E6MTpjaQ
x_CLIENT_SECRET=5dbU41aqT1IY1FRAy4FCMJ1Esn24PM6j22xkcswg_pz3eiJpnf

# Twitter API v1.1 credentials (required for OAuth authentication)
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
